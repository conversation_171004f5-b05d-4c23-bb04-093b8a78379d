import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, ShoppingCart, ListPlus } from 'lucide-react';
import { Product } from '@shared/schema';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';
import { useWallet } from '@/hooks/use-wallet';
import { formatPrice } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { motion, AnimatePresence } from 'framer-motion';
import '@/styles/carousel.css';

interface CarouselSearchResultsProps {
  products: Product[];
  title?: string;
  className?: string;
  aiModeEnabled?: boolean;
  onCurrentProductChange?: (product: Product | null) => void;
}

const CarouselSearchResults: React.FC<CarouselSearchResultsProps> = ({
  products,
  title,
  className = '',
  aiModeEnabled = false,
  onCurrentProductChange
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [direction, setDirection] = useState(0);
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { isPurchaseAllowed } = useWallet();
  const [previousProductIds, setPreviousProductIds] = useState<string[]>([]);

  // Calculate visible products based on screen size
  const [visibleCount, setVisibleCount] = useState(3);
  const [visibleProducts, setVisibleProducts] = useState<Product[]>([]);

  useEffect(() => {
    const handleResize = () => {
      // When AI mode is enabled, always show only 1 product at a time
      if (aiModeEnabled) {
        setVisibleCount(1);
      } else {
        // Regular mode: responsive layout
        if (window.innerWidth < 640) {
          setVisibleCount(1);
        } else if (window.innerWidth < 1024) {
          setVisibleCount(3);
        } else {
          setVisibleCount(3);
        }
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [aiModeEnabled]);

  // Reset currentIndex when products array changes (e.g., sphere toggle)
  useEffect(() => {
    if (products.length === 0) {
      setCurrentIndex(0);
      setPreviousProductIds([]);
      return;
    }

    // Get current product IDs
    const uniqueProducts = Array.from(new Map(products.map(product => [product.id, product])).values());
    const currentProductIds = uniqueProducts.map(p => p.id.toString());

    // Check if the products have actually changed (not just reordered)
    const productsChanged = previousProductIds.length !== currentProductIds.length ||
      !previousProductIds.every(id => currentProductIds.includes(id));

    if (productsChanged) {
      console.log('🎠 Carousel: Products changed, resetting to index 0', {
        oldProducts: previousProductIds,
        newProducts: currentProductIds,
        oldIndex: currentIndex
      });
      setCurrentIndex(0);
      setPreviousProductIds(currentProductIds);
    } else if (currentIndex >= uniqueProducts.length) {
      // If products are the same but currentIndex is out of bounds, reset
      console.log('🎠 Carousel: CurrentIndex out of bounds, resetting', {
        oldIndex: currentIndex,
        newProductsLength: uniqueProducts.length
      });
      setCurrentIndex(0);
    }
  }, [products, currentIndex, previousProductIds]);

  useEffect(() => {
    if (products.length === 0) return;

    // Ensure we have unique products first (safety net)
    const uniqueProducts = Array.from(new Map(products.map(product => [product.id, product])).values());

    // Update visible products when currentIndex or visibleCount changes
    const endIndex = Math.min(currentIndex + visibleCount, uniqueProducts.length);
    const newVisibleProducts = uniqueProducts.slice(currentIndex, endIndex);

    // Debug logging for duplicate detection
    console.log('🎠 Carousel: Updating visible products', {
      totalProducts: products.length,
      uniqueProducts: uniqueProducts.length,
      currentIndex,
      visibleCount,
      endIndex,
      visibleProductIds: newVisibleProducts.map(p => p.id)
    });

    setVisibleProducts(newVisibleProducts);
  }, [currentIndex, visibleCount, products]);

  // Notify parent component of current product change (for AI mode buy functionality)
  useEffect(() => {
    if (aiModeEnabled && onCurrentProductChange && products.length > 0) {
      // In AI mode, we show only 1 product at a time, so the current product is at currentIndex
      const currentProduct = products[currentIndex] || null;
      onCurrentProductChange(currentProduct);
    }
  }, [currentIndex, products, aiModeEnabled, onCurrentProductChange]);

  const handleNext = () => {
    // Ensure we use unique products for navigation
    const uniqueProducts = Array.from(new Map(products.map(product => [product.id, product])).values());

    if (currentIndex + visibleCount < uniqueProducts.length) {
      setDirection(1);
      setCurrentIndex(prev => prev + 1);
      console.log('🎠 Carousel: Next clicked', { currentIndex: currentIndex + 1, totalProducts: uniqueProducts.length });
    }
  };

  const handlePrev = () => {
    if (currentIndex > 0) {
      setDirection(-1);
      setCurrentIndex(prev => prev - 1);
      console.log('🎠 Carousel: Prev clicked', { currentIndex: currentIndex - 1 });
    }
  };

  const handleAddToCart = (product: Product, e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();

    console.log('🛒 Carousel: Adding product to cart:', {
      productId: product.id,
      productTitle: product.title,
      productType: typeof product.id,
      fullProduct: product
    });

    // Check if purchase is allowed with safe card protection
    const purchaseCheck = isPurchaseAllowed(product);
    if (!purchaseCheck.allowed) {
      toast({
        title: "Purchase Blocked",
        description: purchaseCheck.reason,
        variant: "destructive",
        duration: 6000,
      });
      return;
    }

    // Add to cart API call with credentials
    fetch('/api/user/cart/add', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include', // Include cookies for authentication
      body: JSON.stringify({
        productId: Number(product.id), // Ensure it's a number
        quantity: 1,
        source: 'manual'
      })
    })
    .then(response => {
      console.log('🛒 Carousel: Cart response status:', response.status);
      if (!response.ok) {
        return response.json().then(err => {
          console.error('🛒 Carousel: Cart error response:', err);
          if (response.status === 401) {
            throw new Error('Please log in to add items to your cart');
          }
          throw new Error(err.error || 'Failed to add item to cart');
        }).catch(() => {
          // If we can't parse the error response
          if (response.status === 401) {
            throw new Error('Please log in to add items to your cart');
          }
          throw new Error(`Failed to add item to cart (${response.status})`);
        });
      }
      return response.json();
    })
    .then((data) => {
      console.log('🛒 Carousel: Cart success response:', data);

      // Invalidate the cart query to refresh the cart data in the header
      import('@/lib/queryClient').then(module => {
        const { queryClient } = module;
        queryClient.invalidateQueries({ queryKey: ['/api/user/cart'] });
      });

      toast({
        title: "Added to cart",
        description: `${product.title} has been added to your cart.`,
        duration: 3000
      });
    })
    .catch(error => {
      console.error('🛒 Carousel: Error adding item to cart:', error);
      toast({
        title: "Error",
        description: error.message || "Could not add item to cart. Please try again.",
        variant: "destructive",
        duration: 5000
      });
    });
  };

  const handleViewDetails = (productId: number) => {
    setLocation(`/product/${productId}`);
  };


  if (products.length === 0) {
    return null;
  }

  return (
    <div className={`w-full ${className}`}>
      {title && (
        <h2 className="text-xl font-semibold mb-4 text-center">{title}</h2>
      )}

      <div className="carousel-container relative w-full">
        {/* Navigation buttons */}
        <button
          onClick={handlePrev}
          disabled={currentIndex === 0}
          className="carousel-nav-button prev"
          aria-label="Previous"
        >
          <ChevronLeft className="h-5 w-5" />
        </button>

        <button
          onClick={handleNext}
          disabled={currentIndex + visibleCount >= Array.from(new Map(products.map(product => [product.id, product])).values()).length}
          className="carousel-nav-button next"
          aria-label="Next"
        >
          <ChevronRight className="h-5 w-5" />
        </button>

        {/* Product carousel */}
        <div className="overflow-hidden mx-10">
          <div className="carousel-track flex justify-center gap-4">
            <AnimatePresence initial={false} custom={direction}>
              {visibleProducts.map((product, index) => {


                return (
                  <motion.div
                    key={`product-${product.id}-${currentIndex}-${index}`}
                    custom={direction}
                    initial={{
                      opacity: 0,
                      x: direction > 0 ? 200 : -200
                    }}
                    animate={{
                      opacity: 1,
                      x: 0
                    }}
                    exit={{
                      opacity: 0,
                      x: direction < 0 ? 200 : -200
                    }}
                    transition={{ duration: 0.3 }}
                    className="carousel-item w-full sm:w-[calc(33.333%-8px)] lg:w-[calc(33.333%-8px)] flex-shrink-0"
                  >
                    <div className="flex flex-col h-full">
                      {/* Product container with image */}
                      <div
                        className="bg-white dark:bg-gray-800 cursor-pointer overflow-hidden"
                        onClick={() => handleViewDetails(product.id)}
                      >
                        <div className="relative h-[180px] w-full overflow-hidden flex items-center justify-center">
                          <img
                            src={product.imageUrl || '/placeholder-product.svg'}
                            alt={product.title}
                            className="h-auto w-auto max-h-[160px] max-w-[90%] object-contain"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = '/placeholder-product.svg';
                            }}
                          />
                        </div>
                      </div>

                      {/* Product info in separate container */}
                      <div className="text-center py-2">
                        <div className="flex items-center justify-center gap-2">
                          <h3 className="text-sm font-medium text-gray-900 dark:text-white">{product.title}</h3>
                          {product.status === 'sold' && (
                            <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                              SOLD
                            </span>
                          )}
                        </div>
                        <div className="text-sm font-bold text-gray-900 dark:text-white mt-1">{formatPrice(product.price)}</div>
                      </div>

                      {/* Action buttons */}
                      <div className="flex flex-col space-y-2 mt-2">
                        {product.status === 'sold' ? (
                          <Button
                            disabled
                            className="bg-gray-400 text-white rounded-md py-2 text-xs w-full font-medium cursor-not-allowed"
                            size="sm"
                          >
                            SOLD OUT
                          </Button>
                        ) : (
                          <Button
                            onClick={(e) => handleAddToCart(product, e)}
                            className="bg-black hover:bg-gray-800 text-white rounded-md py-2 text-xs w-full font-medium"
                            size="sm"
                          >
                            <ShoppingCart className="mr-2 h-4 w-4" />
                            ADD TO BASKET
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            // TODO: Implement add to list functionality
                            toast({
                              title: "Coming Soon",
                              description: "The 'Add to List' feature will be available soon!",
                              duration: 3000
                            });
                          }}
                          className="border-blue-500 text-blue-600 hover:bg-blue-50 dark:text-blue-400 dark:hover:bg-blue-900/20 rounded-md py-2 text-xs w-full font-medium"
                          size="sm"
                        >
                          <ListPlus className="mr-2 h-4 w-4" />
                          ADD TO LIST
                        </Button>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CarouselSearchResults;
