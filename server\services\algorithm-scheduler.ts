import { RobotPositioningService } from './robot-positioning';
import { log } from '../vite';

export class AlgorithmScheduler {
  private static rotationInterval: NodeJS.Timeout | null = null;
  private static isRunning = false;

  /**
   * Start the hourly algorithm rotation scheduler
   */
  static async start(): Promise<void> {
    if (this.isRunning) {
      log('⚠️ Algorithm scheduler is already running');
      return;
    }

    log('🕐 Starting algorithm rotation scheduler (every hour)...');

    // Initialize algorithms if needed
    await RobotPositioningService.initializeAlgorithms();

    // Run initial rotation to assign algorithms to any existing users
    try {
      await RobotPositioningService.rotateAlgorithms();
      log('✅ Initial algorithm rotation completed');
    } catch (error) {
      log(`❌ Error in initial algorithm rotation: ${error}`, 'error');
    }

    // Set up hourly rotation
    this.rotationInterval = setInterval(async () => {
      try {
        log('🔄 Running scheduled algorithm rotation...');
        await RobotPositioningService.rotateAlgorithms();
        log('✅ Scheduled algorithm rotation completed');
      } catch (error) {
        log(`❌ Error in scheduled algorithm rotation: ${error}`, 'error');
      }
    }, 60 * 60 * 1000); // 1 hour in milliseconds

    this.isRunning = true;
    log('✅ Algorithm scheduler started successfully');
  }

  /**
   * Stop the algorithm rotation scheduler
   */
  static stop(): void {
    if (this.rotationInterval) {
      clearInterval(this.rotationInterval);
      this.rotationInterval = null;
    }
    this.isRunning = false;
    log('🛑 Algorithm scheduler stopped');
  }

  /**
   * Get scheduler status
   */
  static getStatus(): { isRunning: boolean; nextRotation: Date | null } {
    const nextRotation = this.isRunning 
      ? new Date(Date.now() + 60 * 60 * 1000) // Next hour
      : null;

    return {
      isRunning: this.isRunning,
      nextRotation
    };
  }

  /**
   * Manually trigger a rotation (for testing or admin use)
   */
  static async triggerRotation(): Promise<void> {
    log('🔄 Manually triggering algorithm rotation...');
    try {
      await RobotPositioningService.rotateAlgorithms();
      log('✅ Manual algorithm rotation completed');
    } catch (error) {
      log(`❌ Error in manual algorithm rotation: ${error}`, 'error');
      throw error;
    }
  }
}
