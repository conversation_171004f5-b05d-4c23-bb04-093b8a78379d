# DasWos Database Deployment Guide

## Overview

The DasWos ecosystem uses **two separate databases** that work together:

1. **Main Application Database** - PostgreSQL database for the core application
2. **Wallet Database** - Supabase database for wallet authentication and management

## Schema Files

### 📁 unified_schema1.sql
- **Purpose**: Main application database schema
- **Deploy to**: Your main PostgreSQL database
- **Contains**: Users, products, transactions, cart items, etc.

### 📁 unified_schema2.sql  
- **Purpose**: Wallet database schema
- **Deploy to**: Supabase wallet database
- **Contains**: Wallets, wallet sessions, wallet connections, etc.

## Deployment Instructions

### Step 1: Deploy Main Application Database

1. Connect to your main PostgreSQL database
2. Run the entire `unified_schema1.sql` file:
   ```sql
   \i unified_schema1.sql
   ```
   Or copy and paste the contents into your database management tool

### Step 2: Deploy Wallet Database

1. Connect to your Supabase wallet database:
   - URL: `https://mjyaqqsxhkqyzqufpxzl.supabase.co`
   - Use the Supabase SQL editor or connect via psql
2. Run the entire `unified_schema2.sql` file:
   ```sql
   \i unified_schema2.sql
   ```
   Or copy and paste the contents into the Supabase SQL editor

## Database Architecture

```
┌─────────────────────────┐    ┌─────────────────────────┐
│   Main Database         │    │   Wallet Database       │
│   (PostgreSQL)          │    │   (Supabase)            │
├─────────────────────────┤    ├─────────────────────────┤
│ • users                 │    │ • wallets               │
│ • products              │    │ • wallet_sessions       │
│ • purchases             │    │ • wallet_connections    │
│ • daswos_coins_trans... │◄──►│ • transactions (audit)  │
│ • wallet_balances       │    │ • wallet_spending_...   │
│ • cart_items            │    │                         │
│ • categories            │    │                         │
│ • user_sessions         │    │                         │
└─────────────────────────┘    └─────────────────────────┘
```

## Key Features

### Main Database (unified_schema1.sql)
- ✅ User management and authentication
- ✅ Product catalog and categories  
- ✅ Shopping cart functionality
- ✅ Purchase and transaction history
- ✅ DasWos coins balance tracking per wallet
- ✅ Multi-wallet support (up to 5 wallets per user)
- ✅ Seller verification and trust scores
- ✅ Robot positioning algorithms
- ✅ Game functionality

### Wallet Database (unified_schema2.sql)
- ✅ Secure wallet authentication
- ✅ Wallet session management
- ✅ Multi-wallet support (standard, children, business, etc.)
- ✅ Wallet connections to main database users
- ✅ Audit logging for wallet operations
- ✅ Children's wallet spending limits
- ✅ Wallet access tracking

## Important Notes

1. **Two Separate Databases**: These are completely separate databases that communicate via API calls
2. **No Direct Foreign Keys**: The wallet database references users by ID, but there are no direct foreign key constraints between databases
3. **Balance Storage**: Wallet balances are stored in the main database (`wallet_balances` table), not in the wallet database
4. **Security**: Wallet passwords are hashed and stored only in the wallet database
5. **Audit Trail**: All wallet operations are logged in the wallet database for security

## Verification

After deployment, verify the setup:

### Main Database
```sql
SELECT 'Main DB Ready' as status, COUNT(*) as table_count 
FROM information_schema.tables 
WHERE table_schema = 'public';
```

### Wallet Database  
```sql
SELECT 'Wallet DB Ready' as status, COUNT(*) as table_count 
FROM information_schema.tables 
WHERE table_schema = 'public';
```

## Cleanup

After successful deployment, you can safely delete these old schema files:
- `add_app_settings.sql`
- `add_cheesecakes.sql` 
- `add_wallet_products.sql`
- `create_compatible_users.sql`
- `database_fix_unique_constraints.sql`
- `database_migration_*.sql`
- `fix-*.sql`
- `wallet_database_schema_updated.sql`
- All other `.sql` files except `unified_schema1.sql` and `unified_schema2.sql`

## Support

If you encounter any issues during deployment:
1. Check that both databases are accessible
2. Ensure you have the necessary permissions
3. Verify that the required PostgreSQL extensions are available
4. Check the deployment logs for any error messages

---

**✅ Ready for Production**: These schemas include all necessary tables, indexes, constraints, and optimizations for production use.
