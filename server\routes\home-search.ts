import { Router, Request, Response } from 'express';
import { IStorage } from '../../client/src/hooks/storage';

/**
 * Interface for Product objects to improve type safety
 */
interface IProduct {
  id: string;
  title?: string;
  description?: string;
  price?: number;
  imageUrl?: string;
  tags?: string[];
  categoryId?: string;
  sellerId?: string;
  sellerName?: string;
  sellerVerified?: boolean;
  identityVerified?: boolean;
  trustScore?: number;
  createdAt?: string | Date;
}

export function createHomeSearchRoutes(storage: IStorage): Router {
  const router = Router();

  // Home page search endpoint - uses the same deduplication logic as Daswos AI
  router.get('/', async (req: Request, res: Response) => {
    try {
      const { q: searchQuery, sphere = 'safesphere' } = req.query;

      if (!searchQuery || typeof searchQuery !== 'string') {
        return res.status(400).json({ error: 'Search query is required' });
      }

      console.log(`🔍 Home page search: "${searchQuery}" in ${sphere}`);

      // IMPROVED: Clarify deduplication responsibility between storage and application layers
      // The storage layer handles basic deduplication (e.g., DISTINCT ON in SQL)
      // The application layer handles more advanced "perceived duplicate" detection
      let products: IProduct[];
      
      if (searchQuery.trim()) {
        // Get products from storage layer with basic deduplication
        products = await storage.getProducts(sphere as string, searchQuery);
      } else {
        // If no search query, get general products
        products = await storage.getProducts(sphere as string);
      }

      console.log(`🔍 Home page search found ${products.length} products before final filtering`);

      // Apply advanced filtering for home page search
      let filteredProducts: IProduct[] = products;

      // Filter by search query if provided (additional filtering on top of storage layer)
      if (searchQuery.trim()) {
        const searchTerms = searchQuery.toLowerCase().split(' ').filter(term => term.length > 0);

        filteredProducts = products.filter(product => {
          const title = product.title?.toLowerCase() || '';
          const description = product.description?.toLowerCase() || '';
          const tags = product.tags?.join(' ').toLowerCase() || '';
          const sellerName = product.sellerName?.toLowerCase() || '';

          // Product matches if any search term is found in title, description, tags, or seller name
          return searchTerms.some(term =>
            title.includes(term) ||
            description.includes(term) ||
            tags.includes(term) ||
            sellerName.includes(term)
          );
        });
      }

      // Advanced deduplication to prevent perceived duplicates
      filteredProducts = deduplicateSearchResults(filteredProducts, searchQuery);

      // Apply SuperSafe filtering if enabled
      const superSafeEnabled = req.query.superSafeEnabled === 'true';
      const blockGambling = req.query.blockGambling === 'true';
      const blockAdultContent = req.query.blockAdultContent === 'true';

      if (superSafeEnabled) {
        filteredProducts = filteredProducts.filter(product => {
          const title = product.title?.toLowerCase() || '';
          const description = product.description?.toLowerCase() || '';
          const tags = product.tags?.join(' ').toLowerCase() || '';
          
          // Block gambling content
          if (blockGambling) {
            const gamblingTerms = ['casino', 'poker', 'gambling', 'bet', 'lottery', 'jackpot', 'slots'];
            const hasGamblingContent = gamblingTerms.some(term => 
              title.includes(term) || description.includes(term) || tags.includes(term)
            );
            if (hasGamblingContent) return false;
          }
          
          // Block adult content
          if (blockAdultContent) {
            const adultTerms = ['adult', 'xxx', 'porn', 'sex', 'erotic', 'mature'];
            const hasAdultContent = adultTerms.some(term => 
              title.includes(term) || description.includes(term) || tags.includes(term)
            );
            if (hasAdultContent) return false;
          }
          
          return true;
        });
      }

      // Sort products by relevance (same logic as AI recommendations)
      const sortedProducts = filteredProducts.sort((a, b) => {
        // Calculate relevance score for each product
        const scoreA = calculateRelevanceScore(a, searchQuery);
        const scoreB = calculateRelevanceScore(b, searchQuery);
        
        // Sort by relevance score (highest first)
        if (scoreB !== scoreA) {
          return scoreB - scoreA;
        }
        
        // If relevance is equal, sort by trust score
        const trustA = a.trustScore || 0;
        const trustB = b.trustScore || 0;
        if (trustB !== trustA) {
          return trustB - trustA;
        }
        
        // If trust score is equal, sort by creation date (newest first)
        const dateA = new Date(a.createdAt || 0).getTime();
        const dateB = new Date(b.createdAt || 0).getTime();
        return dateB - dateA;
      });

      // Limit results to prevent overwhelming the UI (same as AI recommendations)
      const limitedProducts = sortedProducts.slice(0, 20); // Show up to 20 products

      console.log(`🔍 Home page search final results: ${limitedProducts.length} products`);

      // Final duplicate check before sending response
      const finalUniqueCheck = Array.from(new Map(limitedProducts.map(product => [product.id, product])).values());
      if (limitedProducts.length !== finalUniqueCheck.length) {
        console.error('🚨 CRITICAL: Final response contains duplicates!', {
          original: limitedProducts.length,
          unique: finalUniqueCheck.length,
          duplicateIds: limitedProducts.map(p => p.id).filter((id, index, arr) => arr.indexOf(id) !== index)
        });
      }

      // Log sample results for debugging
      if (limitedProducts.length > 0) {
        console.log('🔍 Sample results:', limitedProducts.slice(0, 3).map(p => ({
          id: p.id,
          title: p.title,
          price: p.price,
          relevanceScore: calculateRelevanceScore(p, searchQuery)
        })));
      }

      res.json(finalUniqueCheck); // Send the deduplicated results

    } catch (error) {
      console.error('Error in home page search:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  });

  return router;
}

/**
 * Calculate relevance score for a product based on search query
 * Higher score = more relevant
 */
function calculateRelevanceScore(product: IProduct, searchQuery: string): number {
  if (!searchQuery || !searchQuery.trim()) {
    return 0;
  }

  let score = 0;
  const query = searchQuery.toLowerCase().trim();
  const searchTerms = query.split(' ').filter(term => term.length > 0);
  
  const title = product.title?.toLowerCase() || '';
  const description = product.description?.toLowerCase() || '';
  const tags = product.tags?.join(' ').toLowerCase() || '';
  const sellerName = product.sellerName?.toLowerCase() || '';

  searchTerms.forEach(term => {
    // Exact title match gets highest score
    if (title === term) {
      score += 100;
    }
    // Title contains term gets high score
    else if (title.includes(term)) {
      score += 50;
    }
    
    // Description contains term gets medium score
    if (description.includes(term)) {
      score += 20;
    }
    
    // Tags contain term gets medium score
    if (tags.includes(term)) {
      score += 15;
    }
    
    // Seller name contains term gets low score
    if (sellerName.includes(term)) {
      score += 5;
    }
  });

  // Boost score for products with higher trust scores
  if (product.trustScore) {
    score += product.trustScore * 0.1;
  }

  // Boost score for verified sellers
  if (product.sellerVerified) {
    score += 10;
  }

  return score;
}

/**
 * Advanced deduplication to prevent perceived duplicates in search results
 * This addresses the broader issue of similar products appearing as duplicates to users
 */
function deduplicateSearchResults(products: IProduct[], searchQuery: string): IProduct[] {
  if (!products || products.length === 0) {
    return products;
  }

  console.log(`🔧 Starting advanced deduplication for ${products.length} products`);

  // Step 1: Remove exact ID duplicates (basic safety net)
  const uniqueById = Array.from(new Map(products.map(product => [product.id, product])).values());

  // Step 2: Group similar products and keep the best representative from each group
  const productGroups = new Map<string, IProduct[]>();

  for (const product of uniqueById) {
    // Create a normalized key for grouping similar products
    const groupKey = createProductGroupKey(product);

    if (!productGroups.has(groupKey)) {
      productGroups.set(groupKey, []);
    }
    productGroups.get(groupKey)!.push(product);
  }

  console.log(`🔧 Grouped ${uniqueById.length} products into ${productGroups.size} groups`);

  // Step 3: From each group, select the best representative
  const deduplicatedProducts: IProduct[] = [];

  for (const [groupKey, groupProducts] of productGroups) {
    if (groupProducts.length === 1) {
      // Single product in group, keep it
      deduplicatedProducts.push(groupProducts[0]);
    } else {
      // Multiple products in group, select the best one
      console.log(`🔧 Group "${groupKey}" has ${groupProducts.length} similar products, selecting best`);

      const bestProduct = selectBestProductFromGroup(groupProducts, searchQuery);
      deduplicatedProducts.push(bestProduct);
    }
  }

  console.log(`🔧 Advanced deduplication: ${products.length} -> ${deduplicatedProducts.length} products`);

  return deduplicatedProducts;
}

/**
 * Create a normalized key for grouping similar products
 * Products with the same key are considered similar enough to be potential duplicates
 */
function createProductGroupKey(product: IProduct): string {
  // Normalize the title by removing common variations
  let normalizedTitle = product.title?.toLowerCase() || '';
  
  // IMPROVED: Basic stemming/lemmatization for common word variations
  // This helps with plurals and other common word forms
  normalizedTitle = normalizedTitle
    .replace(/(\w+)s\b/g, '$1') // Convert plurals to singular (e.g., "shoes" -> "shoe")
    .replace(/(\w+)es\b/g, '$1') // Handle cases like "watches" -> "watch"
    .replace(/(\w+)ies\b/g, '$1y') // Handle cases like "batteries" -> "battery"

  // Remove common prefixes/suffixes that don't change the core product
  const commonPrefixes = ['premium', 'high-quality', 'professional', 'deluxe', 'luxury', 'classic', 'modern', 'vintage', 'new', 'original'];
  const commonSuffixes = ['edition', 'version', 'model', 'style', 'design', 'collection', 'series'];

  // Remove common words that don't affect product identity
  const wordsToRemove = [...commonPrefixes, ...commonSuffixes, 'quality', 'grade', 'top', 'best', 'super', 'ultra', 'mega', 'pro'];

  // Split title into words and filter out common words
  // IMPROVED: Don't filter out words based on length to preserve important short words like "usb", "car", etc.
  const titleWords = normalizedTitle.split(/\s+/).filter(word => {
    return !wordsToRemove.includes(word);
  });

  // Sort words to handle different word orders
  const coreTitle = titleWords.sort().join(' ');

  // Create a key that groups similar products
  // IMPROVED: Use a more granular price range based on the price magnitude
  // For items under $100, group by $5 ranges
  // For items $100-$1000, group by $50 ranges
  // For items over $1000, group by $500 ranges
  let priceRange;
  const price = product.price || 0;
  if (price < 10000) { // Under $100
    priceRange = Math.floor(price / 500) * 500; // Group by $5 ranges
  } else if (price < 100000) { // $100-$1000
    priceRange = Math.floor(price / 5000) * 5000; // Group by $50 ranges
  } else { // Over $1000
    priceRange = Math.floor(price / 50000) * 50000; // Group by $500 ranges
  }

  // Include category to avoid grouping unrelated products
  const category = product.categoryId || 'unknown';
  
  // IMPROVED: Make seller ID optional in the groupKey
  // For a true marketplace experience where users want to see unique products regardless of seller,
  // we can exclude sellerId from the groupKey
  // This allows deduplication of the same product from different sellers
  // If you want to show one product per seller, uncomment the next line
  // const sellerId = product.sellerId || 'unknown';
  
  // Return the group key without seller ID to deduplicate across sellers
  return `${coreTitle}|${priceRange}|${category}`;
}

/**
 * Select the best product from a group of similar products
 * Prioritizes based on quality indicators and relevance to search
 */
function selectBestProductFromGroup(products: IProduct[], searchQuery: string): IProduct {
  if (products.length === 1) {
    return products[0];
  }

  // Score each product based on multiple criteria
  const scoredProducts = products.map(product => ({
    product,
    score: calculateProductQualityScore(product, searchQuery)
  }));

  // Sort by score (highest first) and return the best
  scoredProducts.sort((a, b) => b.score - a.score);

  // IMPROVED: Enhanced logging for better debugging and monitoring
  console.log(`🔧 Selected product "${scoredProducts[0].product.title}" (score: ${scoredProducts[0].score}) from group of ${products.length}`);
  
  // Log detailed information about the selection process for the first few groups
  if (products.length > 1 && products.length <= 5) {
    console.log(`🔧 Selection details for group with ${products.length} products:`);
    scoredProducts.forEach((scored, index) => {
      console.log(`   ${index + 1}. "${scored.product.title}" (ID: ${scored.product.id})`);
      console.log(`      Score: ${scored.score}, Trust: ${scored.product.trustScore || 0}, Price: ${scored.product.price || 0}`);
      if (searchQuery) {
        console.log(`      Relevance: ${calculateRelevanceScore(scored.product, searchQuery)}`);
      }
    });
  }

  return scoredProducts[0].product;
}

/**
 * Calculate a comprehensive quality score for product selection
 */
function calculateProductQualityScore(product: IProduct, searchQuery: string): number {
  let score = 0;

  // IMPROVED: Increase weight of search relevance for better results
  // Search relevance (prioritize this more heavily)
  if (searchQuery) {
    score += calculateRelevanceScore(product, searchQuery) * 0.6; // Increased from 0.3 to 0.6
  }

  // Base quality indicators (slightly reduced weight)
  score += (product.trustScore || 0) * 0.3; // Reduced from 0.5 to 0.3
  score += product.sellerVerified ? 15 : 0; // Reduced from 20 to 15
  score += product.identityVerified ? 10 : 0; // Reduced from 15 to 10

  // Data completeness
  score += product.title ? 10 : 0;
  score += product.description ? 10 : 0;
  score += product.imageUrl ? 5 : 0;
  score += (product.tags && product.tags.length > 0) ? 5 : 0;
  score += product.categoryId ? 5 : 0;

  // Recency bonus (newer products get slight preference)
  if (product.createdAt) {
    const daysSinceCreation = (Date.now() - new Date(product.createdAt).getTime()) / (1000 * 60 * 60 * 24);
    score += Math.max(0, 10 - daysSinceCreation * 0.1); // Up to 10 points for recent products
  }

  // Price competitiveness and reasonableness
  const price = product.price || 0;
  
  // IMPROVED: Avoid extremely high or low prices that might be errors
  if (price > 100 && price < 100000) { // Between $1 and $1000
    score += 5;
  }
  
  // IMPROVED: Add a bonus for competitive pricing
  // This helps select the best-priced product from a group of similar products
  // Lower prices get a higher score, but not so much that it overrides quality
  if (price > 0) {
    // Logarithmic scale to give diminishing returns for very low prices
    // This prevents extremely cheap (potentially error) prices from dominating
    score += Math.max(0, 15 - Math.log10(price / 100) * 3);
  }

  return score;
}
