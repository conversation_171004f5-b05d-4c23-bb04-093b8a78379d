import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DasWosCoinIcon } from '@/components/daswos-coin-icon';
import { ArrowUpRight, User, Wallet, AlertCircle, ArrowRightLeft } from 'lucide-react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';

interface SendMoneyModalProps {
  isOpen: boolean;
  onClose: () => void;
  senderWalletId: string;
  onSuccess?: () => void;
}

export default function SendMoneyModal({
  isOpen,
  onClose,
  senderWalletId,
  onSuccess
}: SendMoneyModalProps) {
  const { toast } = useToast();
  const { user } = useAuth();
  const [transferType, setTransferType] = useState<'external' | 'internal'>('internal');
  const [amount, setAmount] = useState('');
  const [recipientUsername, setRecipientUsername] = useState('');
  const [recipientWalletId, setRecipientWalletId] = useState('');
  const [selectedToWallet, setSelectedToWallet] = useState('');

  // Fetch user's wallets for internal transfers
  const { data: userWallets } = useQuery({
    queryKey: ['/api/multi-wallet/list', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      return apiRequest(`/api/multi-wallet/list?userId=${user.id}`, { method: 'GET' });
    },
    enabled: !!user?.id && transferType === 'internal',
  });

  // External send mutation
  const sendMutation = useMutation({
    mutationFn: async (data: {
      amount: number;
      recipientUsername: string;
      recipientWalletId: string;
      senderWalletId: string;
    }) => {
      return apiRequest('POST', '/api/user/daswos-coins/send', data, {
        credentials: 'include'
      });
    },
    onSuccess: (data) => {
      toast({
        title: 'Money Sent Successfully!',
        description: `Sent ${amount} DasWos Coins to ${recipientUsername}`,
      });
      resetForm();
      onSuccess?.();
    },
    onError: (error: any) => {
      toast({
        title: 'Send Failed',
        description: error.message || 'Failed to send money. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Internal transfer mutation
  const transferMutation = useMutation({
    mutationFn: async (data: {
      amount: number;
      fromWalletId: string;
      toWalletId: string;
    }) => {
      return apiRequest('POST', '/api/user/daswos-coins/transfer', data, {
        credentials: 'include'
      });
    },
    onSuccess: (data) => {
      toast({
        title: 'Transfer Successful!',
        description: `Transferred ${amount} DasWos Coins between wallets`,
      });
      resetForm();
      onSuccess?.();
    },
    onError: (error: any) => {
      toast({
        title: 'Transfer Failed',
        description: error.message || 'Failed to transfer money. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const resetForm = () => {
    setAmount('');
    setRecipientUsername('');
    setRecipientWalletId('');
    setSelectedToWallet('');
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate inputs
    const amountNum = parseInt(amount);
    if (!amountNum || amountNum <= 0) {
      toast({
        title: 'Invalid Amount',
        description: 'Please enter a valid positive amount',
        variant: 'destructive',
      });
      return;
    }

    if (transferType === 'internal') {
      // Internal transfer validation
      if (!selectedToWallet) {
        toast({
          title: 'Missing Destination Wallet',
          description: 'Please select a destination wallet',
          variant: 'destructive',
        });
        return;
      }

      if (selectedToWallet === senderWalletId) {
        toast({
          title: 'Invalid Transfer',
          description: 'Cannot transfer to the same wallet',
          variant: 'destructive',
        });
        return;
      }

      // Execute internal transfer
      transferMutation.mutate({
        amount: amountNum,
        fromWalletId: senderWalletId,
        toWalletId: selectedToWallet
      });
    } else {
      // External transfer validation
      if (!recipientUsername.trim()) {
        toast({
          title: 'Missing Username',
          description: 'Please enter the recipient\'s DasWos username',
          variant: 'destructive',
        });
        return;
      }

      if (!recipientWalletId.trim()) {
        toast({
          title: 'Missing Wallet ID',
          description: 'Please enter the recipient\'s wallet ID',
          variant: 'destructive',
        });
        return;
      }

      // Execute external transfer
      sendMutation.mutate({
        amount: amountNum,
        recipientUsername: recipientUsername.trim(),
        recipientWalletId: recipientWalletId.trim(),
        senderWalletId
      });
    }
  };

  const handleClose = () => {
    if (!sendMutation.isPending && !transferMutation.isPending) {
      resetForm();
      onClose();
    }
  };

  const isLoading = sendMutation.isPending || transferMutation.isPending;

  // Filter out the current wallet from available destination wallets
  const availableWallets = userWallets?.filter((walletId: string) => walletId !== senderWalletId) || [];

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ArrowUpRight className="h-5 w-5" />
            Send DasWos Coins
          </DialogTitle>
        </DialogHeader>

        <Tabs value={transferType} onValueChange={(value) => setTransferType(value as 'external' | 'internal')} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="internal" className="flex items-center gap-2">
              <ArrowRightLeft className="h-4 w-4" />
              Between My Wallets
            </TabsTrigger>
            <TabsTrigger value="external" className="flex items-center gap-2">
              <ArrowUpRight className="h-4 w-4" />
              To Another User
            </TabsTrigger>
          </TabsList>

          <form onSubmit={handleSubmit} className="space-y-4 mt-4">
            {/* Amount Input */}
            <div className="space-y-2">
              <Label htmlFor="amount" className="text-sm font-medium">
                Amount
              </Label>
              <div className="relative">
                <DasWosCoinIcon size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2" />
                <Input
                  id="amount"
                  type="number"
                  placeholder="Enter amount"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="pl-10"
                  min="1"
                  max="1000000"
                  disabled={isLoading}
                />
              </div>
            </div>

            <TabsContent value="internal" className="space-y-4 mt-0">
              {/* Destination Wallet Selection */}
              <div className="space-y-2">
                <Label htmlFor="toWallet" className="text-sm font-medium">
                  Destination Wallet
                </Label>
                <Select value={selectedToWallet} onValueChange={setSelectedToWallet} disabled={isLoading}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select destination wallet" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableWallets.map((walletId: string, index: number) => (
                      <SelectItem key={walletId} value={walletId}>
                        <div className="flex items-center gap-2">
                          <Wallet className="h-4 w-4" />
                          <span>
                            {walletId === user?.primaryWalletId ? 'Primary Wallet' : `Wallet ${index + 1}`}
                          </span>
                          <span className="text-xs text-gray-500">
                            ({walletId.substring(0, 12)}...)
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>

            <TabsContent value="external" className="space-y-4 mt-0">
              {/* Recipient Username */}
              <div className="space-y-2">
                <Label htmlFor="username" className="text-sm font-medium">
                  Recipient Username
                </Label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="username"
                    type="text"
                    placeholder="Enter DasWos username"
                    value={recipientUsername}
                    onChange={(e) => setRecipientUsername(e.target.value)}
                    className="pl-10"
                    disabled={isLoading}
                  />
                </div>
              </div>

              {/* Recipient Wallet ID */}
              <div className="space-y-2">
                <Label htmlFor="walletId" className="text-sm font-medium">
                  Recipient Wallet ID
                </Label>
                <div className="relative">
                  <Wallet className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="walletId"
                    type="text"
                    placeholder="Enter recipient's wallet ID"
                    value={recipientWalletId}
                    onChange={(e) => setRecipientWalletId(e.target.value)}
                    className="pl-10"
                    disabled={isLoading}
                  />
                </div>
              </div>
            </TabsContent>

            {/* Transaction Info */}
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="p-3">
                <div className="flex items-start gap-2">
                  <AlertCircle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div className="text-xs text-blue-800">
                    <p className="font-medium mb-1">Transaction Details:</p>
                    <p>• From wallet: {senderWalletId.substring(0, 12)}...</p>
                    {transferType === 'internal' ? (
                      <>
                        <p>• Transfer between your own wallets</p>
                        <p>• Both wallets must be active</p>
                      </>
                    ) : (
                      <>
                        <p>• This transfer cannot be reversed</p>
                        <p>• Verify recipient details carefully</p>
                      </>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="flex gap-2 pt-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isLoading}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={
                  isLoading ||
                  !amount ||
                  (transferType === 'external' && (!recipientUsername || !recipientWalletId)) ||
                  (transferType === 'internal' && !selectedToWallet)
                }
                className="flex-1"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {transferType === 'internal' ? 'Transferring...' : 'Sending...'}
                  </>
                ) : (
                  <>
                    {transferType === 'internal' ? (
                      <>
                        <ArrowRightLeft className="h-4 w-4 mr-2" />
                        Transfer Coins
                      </>
                    ) : (
                      <>
                        <ArrowUpRight className="h-4 w-4 mr-2" />
                        Send Coins
                      </>
                    )}
                  </>
                )}
              </Button>
            </div>
          </form>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
