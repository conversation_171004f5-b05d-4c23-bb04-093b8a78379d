# Control Bar Requirements - <PERSON><PERSON><PERSON><PERSON> FUNCTIONALITY

## ⚠️ IMPORTANT: These features must ALWAYS be present and functional

### Information Mode Control Bar
When `searchType === 'information'`, the control bar MUST show these buttons in order:

1. **Refine Button** (FIRST BUTTON - MOST IMPORTANT)
   - Text: "Refine" when OFF, "REFINE" when ON
   - Color: Blue when OFF, Yellow when ON
   - Function: `handleRefineToggle`
   - State: `refineMode` boolean

2. **Expand/Minimize Button**
   - Text: "Expand" or "Minimize"
   - Function: Toggle `isAIBoxExpanded`

3. **Refresh Button**
   - Text: "Refresh"
   - Function: `handleAIRefresh`

4. **Clear Button**
   - Text: "Clear"
   - Function: `handleAIClear`

### Shopping Mode Control Bar
When `searchType === 'shopping'`, the control bar MUST show these buttons:

1. **Expand Button**
   - Text: "Expand"
   - Function: `handleProductExpand`
   - Opens product detail popup

2. **Refresh Button**
   - Text: "Refresh"
   - Function: `handleShoppingRefresh`

3. **Clear Button**
   - Text: "Clear"
   - Function: `handleAIClear`

### Required State Variables
These state variables MUST exist and NEVER be removed:

```typescript
// Refine mode states - CRITICAL: DO NOT REMOVE THESE
const [refineMode, setRefineMode] = useState<boolean>(false);
const [showRefineGuide, setShowRefineGuide] = useState<boolean>(false);

// Product expansion states - CRITICAL: DO NOT REMOVE THESE
const [isProductExpanded, setIsProductExpanded] = useState<boolean>(false);
const [currentProduct, setCurrentProduct] = useState<any>(null);
```

### Required Handler Functions
These functions MUST exist and NEVER be removed:

```typescript
const handleRefineToggle = () => { /* toggles refine mode */ };
const handleProductExpand = () => { /* opens product popup */ };
const handleProductClose = () => { /* closes product popup */ };
const handleShoppingRefresh = () => { /* refreshes shopping results */ };
```

### Control Bar Visibility
The control bar MUST show when: `showResults` is true
- NO other conditions should hide it
- It should work in both information and shopping modes

### Refine Mode Logic
- When `refineMode` is OFF: searches are independent, no context used
- When `refineMode` is ON: searches use accumulated context
- InformationResults component receives:
  - `searchQuery`: `refineMode && searchContext.length > 0 ? searchContext.join(' ') : searchQuery`
  - `searchContext`: `refineMode ? searchContext : []`

## Testing Checklist
- [ ] Refine button appears in information mode
- [ ] Refine button toggles between "Refine" and "REFINE"
- [ ] Refine button changes color (blue/yellow)
- [ ] Shopping mode shows Expand, Refresh, Clear buttons
- [ ] Control bar appears whenever there are search results
- [ ] Refine mode OFF: independent searches
- [ ] Refine mode ON: contextual searches

## Common Issues and Solutions
1. **Buttons disappearing**: Check control bar visibility condition
2. **Refine button missing**: Ensure it's the FIRST button in information mode
3. **State variables missing**: Re-add the critical state variables
4. **Handler functions missing**: Re-add the required handler functions
