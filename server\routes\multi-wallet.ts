import express from 'express';
import { createClient } from '@supabase/supabase-js';
import { isAuthenticated } from '../middleware/auth';
import { db } from '../db';
import { users } from '../../shared/schema1';
import { eq } from 'drizzle-orm';
import bcrypt from 'bcrypt';

const router = express.Router();

// Wallet database configuration
const WALLET_DATABASE_CONFIG = {
  SUPABASE_URL: 'https://mjyaqqsxhkqyzqufpxzl.supabase.co',
  SUPABASE_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1qeWFxcXN4aGtxeXpxdWZweHpsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYyMzU4MiwiZXhwIjoyMDY0MTk5NTgyfQ.1LBqR-7c8UdBa8koW69nQrYSr_Lm1aErRUgoRteFFVs'
};

// Initialize Supabase client for wallet database
const walletSupabase = createClient(
  WALLET_DATABASE_CONFIG.SUPABASE_URL,
  WALLET_DATABASE_CONFIG.SUPABASE_KEY
);

/**
 * GET /api/multi-wallet/list
 * Get a simple list of activated wallet IDs for the current user
 */
router.get('/list', isAuthenticated, async (req, res) => {
  try {
    console.log('🔍 /api/multi-wallet/list called for user:', req.user?.id);
    const userId = req.user?.id;
    if (!userId) {
      console.log('❌ /api/multi-wallet/list: User not authenticated');
      return res.status(401).json({ error: 'User not authenticated' });
    }

    // Get user's wallet information from main database
    const user = await db.select().from(users).where(eq(users.id, userId)).limit(1);
    if (!user.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userData = user[0];
    const userWalletIds = userData.walletIds || [];

    console.log(`📋 Multi-wallet list request for user ${userData.username} (${userData.id}):`, {
      userWalletIds,
      primaryWalletId: userData.primaryWalletId,
      activeWalletId: userData.activeWalletId
    });

    // If user has no wallet IDs, return empty array
    if (userWalletIds.length === 0) {
      console.log('📋 User has no wallet IDs, returning empty array');
      return res.json([]);
    }

    // Check which wallets are actually activated (have real passwords set) in the wallet database
    const { data: activatedWallets, error } = await walletSupabase
      .from('wallets')
      .select('wallet_id')
      .in('wallet_id', userWalletIds)
      .not('password_hash', 'is', null) // Only wallets with passwords set
      .not('password_hash', 'eq', 'TEMP_PENDING_ACTIVATION') // Exclude temporary passwords
      .eq('is_active', true);

    if (error) {
      console.error('Error checking wallet activation status:', error);

      // FALLBACK: If wallet database is not available (tables don't exist),
      // check activation status using the main database wallet creation logic
      if (error.code === '42P01') { // Table doesn't exist
        console.log('📋 Wallet database not available, using fallback activation check');
        console.log('📋 Returning all user wallet IDs as fallback:', userWalletIds);

        // Return all wallet IDs since we can't check activation status
        // This allows the UI to work even without the wallet database
        return res.json(userWalletIds);
      }

      // For other errors, still fallback to returning all wallet IDs
      console.log('📋 Database error, returning all user wallet IDs as fallback:', userWalletIds);
      return res.json(userWalletIds);
    }

    // Return only the wallet IDs that are activated
    const activatedWalletIds = activatedWallets?.map(w => w.wallet_id) || [];
    console.log(`📋 Found ${activatedWalletIds.length} activated wallets for user:`, activatedWalletIds);

    // Return in the format expected by frontend
    res.json({
      wallets: activatedWalletIds,
      user: userData.username,
      primaryWalletId: userData.primaryWalletId
    });

  } catch (error) {
    console.error('Error in GET /api/multi-wallet/list:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/multi-wallet/user/:user_id
 * Get all wallets for a user
 */
router.get('/user/:user_id', isAuthenticated, async (req, res) => {
  try {
    const { user_id } = req.params;
    const userId = parseInt(user_id);

    // Get user's wallet information from main database
    const user = await db.select().from(users).where(eq(users.id, userId)).limit(1);
    if (!user.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userData = user[0];

    // Get wallet connections from wallet database (simplified to avoid join issues)
    const { data: walletConnections, error } = await walletSupabase
      .from('wallet_connections')
      .select('*')
      .eq('user_id', userId)
      .eq('database_name', 'daswos-18')
      .eq('is_active', true)
      .order('wallet_order', { ascending: true });

    if (error) {
      console.error('Error fetching user wallets:', error);
      return res.status(500).json({
        error: 'Failed to fetch user wallets',
        details: error.message
      });
    }

    res.json({
      success: true,
      user: {
        id: userData.id,
        username: userData.username,
        primaryWalletId: userData.primaryWalletId,
        activeWalletId: userData.activeWalletId,
        walletIds: userData.walletIds || [],
        walletNicknames: userData.walletNicknames || {}
      },
      wallets: walletConnections || [],
      walletCount: walletConnections?.length || 0,
      maxWallets: 5
    });

  } catch (error) {
    console.error('Error in GET /api/multi-wallet/user/:user_id:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/multi-wallet/create
 * Create a new wallet for a user
 */
router.post('/create', isAuthenticated, async (req, res) => {
  try {
    const {
      userId,
      username,
      walletType = 'standard',
      walletName,
      walletDescription,
      password,
      // Children's wallet specific fields
      isChildrenWallet = false,
      parentWalletId,
      ageRestriction,
      spendingLimitDaily = 0,
      spendingLimitMonthly = 0,
      allowedCategories = [],
      restrictedFeatures = []
    } = req.body;

    // Validate required fields
    if (!userId || !username || !password) {
      return res.status(400).json({
        error: 'Missing required fields: userId, username, password'
      });
    }

    // Check if user exists
    const user = await db.select().from(users).where(eq(users.id, userId)).limit(1);
    if (!user.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userData = user[0];

    // Check wallet limit (max 5 wallets per user)
    const currentWalletCount = userData.walletIds?.length || 0;
    if (currentWalletCount >= 5) {
      return res.status(400).json({
        error: 'Maximum wallet limit reached',
        message: 'Users can have a maximum of 5 wallets'
      });
    }

    // Validate children's wallet requirements
    if (isChildrenWallet && !parentWalletId) {
      return res.status(400).json({
        error: 'Parent wallet ID is required for children\'s wallets'
      });
    }

    // Generate wallet ID based on type
    let walletIdFunction;
    switch (walletType) {
      case 'children':
        walletIdFunction = 'generate_children_wallet_id';
        break;
      case 'business':
        walletIdFunction = 'generate_business_wallet_id';
        break;
      default:
        walletIdFunction = 'generate_standard_wallet_id';
    }

    // Generate unique wallet ID
    const { data: walletIdResult, error: walletIdError } = await walletSupabase
      .rpc(walletIdFunction);

    if (walletIdError || !walletIdResult) {
      console.error('Error generating wallet ID:', walletIdError);
      return res.status(500).json({
        error: 'Failed to generate wallet ID',
        details: walletIdError?.message
      });
    }

    const newWalletId = walletIdResult;

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Set default restricted features for children's wallets
    const defaultRestrictedFeatures = isChildrenWallet
      ? ['opensphere', 'gambling', 'adult_content', ...restrictedFeatures]
      : restrictedFeatures;

    // Create wallet in wallet database
    const { data: newWallet, error: walletError } = await walletSupabase
      .from('wallets')
      .insert({
        wallet_id: newWalletId,
        password_hash: passwordHash,
        wallet_type: walletType,
        wallet_name: walletName,
        wallet_description: walletDescription,
        is_children_wallet: isChildrenWallet,
        parent_wallet_id: parentWalletId,
        age_restriction: ageRestriction,
        spending_limit_daily: spendingLimitDaily,
        spending_limit_monthly: spendingLimitMonthly,
        allowed_categories: allowedCategories,
        restricted_features: defaultRestrictedFeatures,
        creation_ip: req.ip
      })
      .select()
      .single();

    if (walletError) {
      console.error('Error creating wallet:', walletError);
      return res.status(500).json({
        error: 'Failed to create wallet',
        details: walletError.message
      });
    }

    // Determine wallet order (next available slot)
    const nextWalletOrder = currentWalletCount + 1;

    // Create wallet connection
    const { data: connection, error: connectionError } = await walletSupabase
      .from('wallet_connections')
      .insert({
        wallet_id: newWallet.id,
        database_name: 'daswos-18',
        user_id: userId,
        username: username,
        wallet_order: nextWalletOrder,
        wallet_nickname: walletName,
        is_primary: currentWalletCount === 0, // First wallet is primary
        is_active: true
      })
      .select()
      .single();

    if (connectionError) {
      console.error('Error creating wallet connection:', connectionError);
      // Clean up the wallet if connection failed
      await walletSupabase.from('wallets').delete().eq('id', newWallet.id);
      return res.status(500).json({
        error: 'Failed to create wallet connection',
        details: connectionError.message
      });
    }

    // Update user's wallet information in main database
    const updatedWalletIds = [...(userData.walletIds || []), newWalletId];
    const updatedWalletNicknames = {
      ...(userData.walletNicknames || {}),
      [newWalletId]: walletName || `Wallet ${nextWalletOrder}`
    };

    // Prepare update data
    const updateData: any = {
      walletIds: updatedWalletIds,
      walletNicknames: updatedWalletNicknames,
      activeWalletId: userData.activeWalletId || newWalletId // Set as active if first wallet
    };

    // SECURITY: Only set primaryWalletId if it's not already set (first wallet only)
    if (!userData.primaryWalletId) {
      updateData.primaryWalletId = newWalletId;
      console.log(`🔒 Setting primary wallet ID for first time: ${newWalletId}`);
    } else {
      console.log(`🔒 Primary wallet ID already set, keeping: ${userData.primaryWalletId}`);
    }

    await db.update(users)
      .set(updateData)
      .where(eq(users.id, userId));

    res.json({
      success: true,
      message: 'Wallet created successfully',
      wallet: {
        id: newWallet.id,
        walletId: newWalletId,
        walletType: walletType,
        walletName: walletName,
        isChildrenWallet: isChildrenWallet,
        walletOrder: nextWalletOrder,
        isPrimary: currentWalletCount === 0
      },
      connection: connection
    });

  } catch (error) {
    console.error('Error in POST /api/multi-wallet/create:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * PUT /api/multi-wallet/set-active
 * Set the active wallet for a user
 */
router.put('/set-active', isAuthenticated, async (req, res) => {
  try {
    const { userId, walletId } = req.body;

    if (!userId || !walletId) {
      return res.status(400).json({
        error: 'Missing required fields: userId, walletId'
      });
    }

    // Get user data
    const user = await db.select().from(users).where(eq(users.id, userId)).limit(1);
    if (!user.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userData = user[0];

    // Check if wallet belongs to user
    if (!userData.walletIds?.includes(walletId)) {
      return res.status(403).json({
        error: 'Wallet does not belong to user'
      });
    }

    // Update active wallet
    await db.update(users)
      .set({ activeWalletId: walletId })
      .where(eq(users.id, userId));

    res.json({
      success: true,
      message: 'Active wallet updated successfully',
      activeWalletId: walletId
    });

  } catch (error) {
    console.error('Error in PUT /api/multi-wallet/set-active:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * REMOVED: PUT /api/multi-wallet/set-primary
 * Primary wallet should NEVER be changed - it's always the first wallet created
 * Use PUT /api/multi-wallet/set-active to change the active wallet instead
 */

/**
 * DELETE /api/multi-wallet/delete
 * Delete a wallet (cannot delete primary wallet)
 */
router.delete('/delete', isAuthenticated, async (req, res) => {
  try {
    const { userId, walletId } = req.body;

    if (!userId || !walletId) {
      return res.status(400).json({
        error: 'Missing required fields: userId, walletId'
      });
    }

    // Get user data
    const user = await db.select().from(users).where(eq(users.id, userId)).limit(1);
    if (!user.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userData = user[0];

    // Check if wallet belongs to user
    if (!userData.walletIds?.includes(walletId)) {
      return res.status(403).json({
        error: 'Wallet does not belong to user'
      });
    }

    // Cannot delete primary wallet
    if (userData.primaryWalletId === walletId) {
      return res.status(400).json({
        error: 'Cannot delete primary wallet',
        message: 'Please set another wallet as primary before deleting this wallet'
      });
    }

    // Get wallet details to check if it's a parent wallet
    const { data: wallet, error: walletError } = await walletSupabase
      .from('wallets')
      .select('*')
      .eq('wallet_id', walletId)
      .single();

    if (walletError) {
      return res.status(404).json({ error: 'Wallet not found' });
    }

    // Check if this wallet has children wallets
    const { data: childrenWallets, error: childrenError } = await walletSupabase
      .from('wallets')
      .select('wallet_id')
      .eq('parent_wallet_id', wallet.id);

    if (childrenError) {
      console.error('Error checking children wallets:', childrenError);
    }

    if (childrenWallets && childrenWallets.length > 0) {
      return res.status(400).json({
        error: 'Cannot delete wallet with children wallets',
        message: 'Please delete or reassign children wallets first',
        childrenWallets: childrenWallets.map(w => w.wallet_id)
      });
    }

    // Delete wallet connection
    const { error: connectionDeleteError } = await walletSupabase
      .from('wallet_connections')
      .delete()
      .eq('user_id', userId)
      .eq('database_name', 'daswos-18')
      .in('wallet_id', [walletId]);

    if (connectionDeleteError) {
      console.error('Error deleting wallet connection:', connectionDeleteError);
      return res.status(500).json({
        error: 'Failed to delete wallet connection',
        details: connectionDeleteError.message
      });
    }

    // Delete wallet
    const { error: walletDeleteError } = await walletSupabase
      .from('wallets')
      .delete()
      .eq('id', wallet.id);

    if (walletDeleteError) {
      console.error('Error deleting wallet:', walletDeleteError);
      return res.status(500).json({
        error: 'Failed to delete wallet',
        details: walletDeleteError.message
      });
    }

    // Update user's wallet information in main database
    const updatedWalletIds = userData.walletIds?.filter(id => id !== walletId) || [];
    const updatedWalletNicknames = { ...(userData.walletNicknames || {}) };
    delete updatedWalletNicknames[walletId];

    // If active wallet was deleted, set to primary
    const newActiveWalletId = userData.activeWalletId === walletId
      ? userData.primaryWalletId
      : userData.activeWalletId;

    await db.update(users)
      .set({
        walletIds: updatedWalletIds,
        walletNicknames: updatedWalletNicknames,
        activeWalletId: newActiveWalletId
      })
      .where(eq(users.id, userId));

    res.json({
      success: true,
      message: 'Wallet deleted successfully',
      deletedWalletId: walletId,
      remainingWallets: updatedWalletIds.length
    });

  } catch (error) {
    console.error('Error in DELETE /api/multi-wallet/delete:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/multi-wallet/check-restrictions/:wallet_id
 * Check if a wallet has restrictions for certain actions
 */
router.get('/check-restrictions/:wallet_id', isAuthenticated, async (req, res) => {
  try {
    const { wallet_id } = req.params;
    const { action, category, amount } = req.query;

    // Get wallet details
    const { data: wallet, error } = await walletSupabase
      .from('wallets')
      .select('*')
      .eq('wallet_id', wallet_id)
      .single();

    if (error || !wallet) {
      return res.status(404).json({ error: 'Wallet not found' });
    }

    const restrictions = {
      allowed: true,
      reasons: [] as string[],
      walletType: wallet.wallet_type,
      isChildrenWallet: wallet.is_children_wallet
    };

    // Check children's wallet restrictions
    if (wallet.is_children_wallet) {
      // Check restricted features
      if (action && wallet.restricted_features?.includes(action)) {
        restrictions.allowed = false;
        restrictions.reasons.push(`Action '${action}' is restricted for children's wallets`);
      }

      // Check category restrictions
      if (category && wallet.allowed_categories?.length > 0 && !wallet.allowed_categories.includes(category)) {
        restrictions.allowed = false;
        restrictions.reasons.push(`Category '${category}' is not allowed for this children's wallet`);
      }

      // Check spending limits
      if (amount) {
        const spendingAmount = parseInt(amount as string);

        if (wallet.spending_limit_daily > 0 && spendingAmount > wallet.spending_limit_daily) {
          restrictions.allowed = false;
          restrictions.reasons.push(`Amount exceeds daily spending limit of $${(wallet.spending_limit_daily / 100).toFixed(2)}`);
        }

        // TODO: Check actual daily/monthly spending against limits
        // This would require querying the spending tracking table
      }
    }

    res.json({
      success: true,
      walletId: wallet_id,
      restrictions
    });

  } catch (error) {
    console.error('Error in GET /api/multi-wallet/check-restrictions:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/multi-wallet/update-nickname
 * Update wallet nickname for the current user
 */
router.post('/update-nickname', isAuthenticated, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const { walletId, nickname } = req.body;
    if (!walletId) {
      return res.status(400).json({ error: 'Wallet ID is required' });
    }

    // Get user's current wallet information
    const user = await db.select().from(users).where(eq(users.id, userId)).limit(1);
    if (!user.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userData = user[0];
    const userWalletIds = userData.walletIds || [];

    // Verify user owns this wallet
    if (!userWalletIds.includes(walletId)) {
      return res.status(403).json({ error: 'Access denied: Wallet not owned by user' });
    }

    // Update wallet nicknames
    const currentNicknames = userData.walletNicknames || {};
    const updatedNicknames = { ...currentNicknames };

    if (nickname && nickname.trim()) {
      updatedNicknames[walletId] = nickname.trim();
    } else {
      // Remove nickname if empty
      delete updatedNicknames[walletId];
    }

    await db
      .update(users)
      .set({ walletNicknames: updatedNicknames })
      .where(eq(users.id, userId));

    res.json({
      success: true,
      message: 'Wallet nickname updated successfully',
      walletId,
      nickname: nickname?.trim() || null
    });

  } catch (error) {
    console.error('Error in POST /api/multi-wallet/update-nickname:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
