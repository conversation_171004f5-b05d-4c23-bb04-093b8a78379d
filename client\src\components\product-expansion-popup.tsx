import React, { useState } from 'react';
import { X, ShoppingCart, Star, Heart, Share2, ExternalLink, Shield, CheckCircle, AlertTriangle, Package, Truck, CreditCard, Info } from 'lucide-react';

interface ProductExpansionPopupProps {
  product: any;
  isOpen: boolean;
  onClose: () => void;
  onAddToCart?: (product: any) => void;
}

const ProductExpansionPopup: React.FC<ProductExpansionPopupProps> = ({
  product,
  isOpen,
  onClose,
  onAddToCart
}) => {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);

  if (!isOpen || !product) return null;

  // Extract product data with fallbacks
  const productData = {
    title: product.title || product.name || 'Unknown Product',
    description: product.description || 'No description available',
    price: product.price || 0,
    originalPrice: product.originalPrice,
    discount: product.discount,
    imageUrl: product.imageUrl || product.image || '/placeholder-product.svg',
    images: product.images || [product.imageUrl || product.image || '/placeholder-product.svg'],
    sellerName: product.sellerName || product.seller?.name || 'Unknown Seller',
    sellerVerified: product.sellerVerified || product.seller?.verified || false,
    trustScore: product.trustScore || 0,
    rating: product.rating || (product.trustScore ? Math.min(5, Math.max(1, product.trustScore / 20)) : 0),
    tags: product.tags || [],
    shipping: product.shipping || 'Standard shipping',
    inStock: product.inStock !== false && product.quantity > 0,
    quantity: product.quantity || 1,
    warning: product.warning,
    verifiedSince: product.verifiedSince,
    isBulkBuy: product.isBulkBuy || false,
    bulkMinimumQuantity: product.bulkMinimumQuantity,
    bulkDiscountRate: product.bulkDiscountRate
  };

  const handleAddToCart = () => {
    if (onAddToCart) {
      onAddToCart({ ...product, selectedQuantity: quantity });
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const formatPrice = (price: number) => {
    return (price / 100).toFixed(2);
  };

  const getTrustScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-500';
    if (score >= 60) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getTrustScoreLabel = (score: number) => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    return 'Fair';
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-6xl w-full max-h-[95vh] overflow-y-auto shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
          <div className="flex items-center space-x-3">
            <Package className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              Product Details
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-full transition-colors"
            title="Close"
          >
            <X className="h-6 w-6 text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Product Image Gallery */}
            <div className="space-y-4">
              {/* Main Image */}
              <div className="aspect-square bg-gray-100 dark:bg-gray-700 rounded-xl overflow-hidden shadow-lg">
                <img
                  src={productData.images[selectedImageIndex]}
                  alt={productData.title}
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/placeholder-product.svg';
                  }}
                />
              </div>

              {/* Image Thumbnails */}
              {productData.images.length > 1 && (
                <div className="grid grid-cols-4 gap-3">
                  {productData.images.slice(0, 4).map((img: string, index: number) => (
                    <div
                      key={index}
                      className={`aspect-square bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden cursor-pointer border-2 transition-all duration-200 ${
                        selectedImageIndex === index
                          ? 'border-blue-500 shadow-md'
                          : 'border-transparent hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedImageIndex(index)}
                    >
                      <img
                        src={img}
                        alt={`${productData.title} ${index + 1}`}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = '/placeholder-product.svg';
                        }}
                      />
                    </div>
                  ))}
                </div>
              )}

              {/* Trust & Safety Indicators */}
              <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 space-y-3">
                <h4 className="font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                  <Shield className="h-4 w-4 mr-2 text-blue-600" />
                  Trust & Safety
                </h4>

                <div className="space-y-2">
                  {/* Seller Verification */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Seller Status:</span>
                    <div className="flex items-center">
                      {productData.sellerVerified ? (
                        <>
                          <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                          <span className="text-sm text-green-600 dark:text-green-400">Verified</span>
                        </>
                      ) : (
                        <>
                          <AlertTriangle className="h-4 w-4 text-yellow-500 mr-1" />
                          <span className="text-sm text-yellow-600 dark:text-yellow-400">Unverified</span>
                        </>
                      )}
                    </div>
                  </div>

                  {/* Trust Score */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Trust Score:</span>
                    <span className={`text-sm font-medium ${getTrustScoreColor(productData.trustScore)}`}>
                      {productData.trustScore}/100 ({getTrustScoreLabel(productData.trustScore)})
                    </span>
                  </div>

                  {/* Verified Since */}
                  {productData.verifiedSince && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Verified Since:</span>
                      <span className="text-sm text-gray-900 dark:text-gray-100">{productData.verifiedSince}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Product Info */}
            <div className="space-y-6">
              {/* Title and Price */}
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-3">
                  {productData.title}
                </h1>

                {/* Price Section */}
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <span className="text-4xl font-bold text-blue-600 dark:text-blue-400">
                        ${formatPrice(productData.price)}
                      </span>
                      {productData.originalPrice && productData.originalPrice > productData.price && (
                        <>
                          <span className="text-xl text-gray-500 line-through">
                            ${formatPrice(productData.originalPrice)}
                          </span>
                          {productData.discount && (
                            <span className="bg-red-500 text-white px-2 py-1 rounded-md text-sm font-medium">
                              -{productData.discount}%
                            </span>
                          )}
                        </>
                      )}
                    </div>

                    {/* Stock Status */}
                    <div className="text-right">
                      {productData.inStock ? (
                        <div className="flex items-center text-green-600 dark:text-green-400">
                          <CheckCircle className="h-4 w-4 mr-1" />
                          <span className="text-sm font-medium">In Stock</span>
                        </div>
                      ) : (
                        <div className="flex items-center text-red-600 dark:text-red-400">
                          <AlertTriangle className="h-4 w-4 mr-1" />
                          <span className="text-sm font-medium">Out of Stock</span>
                        </div>
                      )}
                      {productData.quantity > 0 && (
                        <p className="text-xs text-gray-500 mt-1">{productData.quantity} available</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Warning */}
                {productData.warning && (
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3 mb-4">
                    <div className="flex items-start">
                      <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5" />
                      <div>
                        <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">Warning</h4>
                        <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">{productData.warning}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Rating */}
              {productData.rating > 0 && (
                <div className="flex items-center space-x-3">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-5 w-5 ${
                          i < Math.floor(productData.rating)
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-lg font-medium text-gray-900 dark:text-gray-100">
                    {productData.rating.toFixed(1)}
                  </span>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    (Based on trust score)
                  </span>
                </div>
              )}

              {/* Description */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                  <Info className="h-5 w-5 mr-2 text-blue-600" />
                  Description
                </h3>
                <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    {productData.description}
                  </p>
                </div>
              </div>

              {/* Tags */}
              {productData.tags.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
                    Tags
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {productData.tags.map((tag: string, index: number) => (
                      <span
                        key={index}
                        className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Shipping & Delivery */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                  <Truck className="h-5 w-5 mr-2 text-blue-600" />
                  Shipping & Delivery
                </h3>
                <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
                  <p className="text-gray-700 dark:text-gray-300">
                    {productData.shipping}
                  </p>
                </div>
              </div>

              {/* Bulk Buy Information */}
              {productData.isBulkBuy && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
                    Bulk Purchase Options
                  </h3>
                  <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                    <div className="space-y-2">
                      {productData.bulkMinimumQuantity && (
                        <p className="text-sm text-green-700 dark:text-green-300">
                          <span className="font-medium">Minimum Quantity:</span> {productData.bulkMinimumQuantity} units
                        </p>
                      )}
                      {productData.bulkDiscountRate && (
                        <p className="text-sm text-green-700 dark:text-green-300">
                          <span className="font-medium">Bulk Discount:</span> {productData.bulkDiscountRate}% off
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Quantity Selector */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
                  Quantity
                </h3>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="w-10 h-10 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 rounded-lg flex items-center justify-center transition-colors"
                    disabled={quantity <= 1}
                  >
                    -
                  </button>
                  <span className="text-xl font-semibold text-gray-900 dark:text-gray-100 min-w-[3rem] text-center">
                    {quantity}
                  </span>
                  <button
                    onClick={() => setQuantity(Math.min(productData.quantity, quantity + 1))}
                    className="w-10 h-10 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 rounded-lg flex items-center justify-center transition-colors"
                    disabled={quantity >= productData.quantity}
                  >
                    +
                  </button>
                  <span className="text-sm text-gray-500 dark:text-gray-400 ml-2">
                    (Max: {productData.quantity})
                  </span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col space-y-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={handleAddToCart}
                  disabled={!productData.inStock}
                  className={`w-full font-semibold py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-3 ${
                    productData.inStock
                      ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl'
                      : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                  }`}
                >
                  <ShoppingCart className="h-6 w-6" />
                  <span className="text-lg">
                    {productData.inStock ? 'Add to Cart' : 'Out of Stock'}
                  </span>
                </button>

                <div className="grid grid-cols-3 gap-3">
                  <button className="bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2">
                    <Heart className="h-4 w-4" />
                    <span>Save</span>
                  </button>

                  <button className="bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2">
                    <Share2 className="h-4 w-4" />
                    <span>Share</span>
                  </button>

                  <button className="bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2">
                    <ExternalLink className="h-4 w-4" />
                    <span>Details</span>
                  </button>
                </div>
              </div>

              {/* Seller Info */}
              <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                  <CreditCard className="h-5 w-5 mr-2 text-blue-600" />
                  Seller Information
                </h3>
                <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Seller Name:</span>
                    <span className="font-medium text-gray-900 dark:text-gray-100">
                      {productData.sellerName}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Verification Status:</span>
                    <div className="flex items-center">
                      {productData.sellerVerified ? (
                        <>
                          <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                          <span className="text-green-600 dark:text-green-400 font-medium">Verified</span>
                        </>
                      ) : (
                        <>
                          <AlertTriangle className="h-4 w-4 text-yellow-500 mr-1" />
                          <span className="text-yellow-600 dark:text-yellow-400 font-medium">Unverified</span>
                        </>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Trust Score:</span>
                    <div className="flex items-center">
                      <div className={`w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full mr-2`}>
                        <div
                          className={`h-2 rounded-full ${
                            productData.trustScore >= 80 ? 'bg-green-500' :
                            productData.trustScore >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${productData.trustScore}%` }}
                        />
                      </div>
                      <span className={`font-medium ${getTrustScoreColor(productData.trustScore)}`}>
                        {productData.trustScore}%
                      </span>
                    </div>
                  </div>

                  {productData.verifiedSince && (
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Member Since:</span>
                      <span className="text-gray-900 dark:text-gray-100">{productData.verifiedSince}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductExpansionPopup;
