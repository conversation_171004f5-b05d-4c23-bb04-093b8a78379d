-- Migration: Add safe card support to wallet_cards table
-- Date: 2025-01-14
-- Description: Adds is_safe_card column to support safe cards with automatic safety features
-- Note: This replaces the previous child card concept with safe cards

-- Add is_safe_card column to wallet_cards table in main database
ALTER TABLE wallet_cards
ADD COLUMN is_safe_card BO<PERSON>EAN DEFAULT FALSE NOT NULL;

-- Add comment to document the column purpose
COMMENT ON COLUMN wallet_cards.is_safe_card IS 'Indicates if this is a safe card with automatic SafeSphere and SuperSafe protection';

-- Update any existing cards to explicitly set is_safe_card = false (for clarity)
UPDATE wallet_cards SET is_safe_card = FALSE WHERE is_safe_card IS NULL;

-- Create index for efficient querying of safe cards
CREATE INDEX idx_wallet_cards_is_safe_card ON wallet_cards(is_safe_card);

-- For wallet database (Supabase), run this separately:
-- ALTER TABLE wallet_cards
-- ADD COLUMN is_safe_card B<PERSON><PERSON><PERSON>N DEFAULT FALSE NOT NULL;
