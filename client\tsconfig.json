{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "jsxImportSource": "react", "strict": true, "esModuleInterop": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["vite/client"], "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["src"], "exclude": ["node_modules"], "references": [{"path": "./tsconfig.node.json"}]}