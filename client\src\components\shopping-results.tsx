import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Loader2 } from 'lucide-react';
import CarouselSearchResults from '@/components/carousel-search-results';
import { Product } from '@shared/schema';
import { useSuperSafe } from '@/contexts/super-safe-context';

interface ShoppingResultsProps {
  searchQuery: string;
  searchContext?: string[];
  sphere: 'safesphere' | 'opensphere';
  className?: string;
  aiModeEnabled?: boolean;
  onCurrentProductChange?: (product: Product | null) => void;
}

const ShoppingResults: React.FC<ShoppingResultsProps> = ({
  searchQuery,
  searchContext = [],
  sphere,
  className = '',
  aiModeEnabled = false,
  onCurrentProductChange
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [products, setProducts] = useState<Product[]>([]);

  // Use the same SuperSafe hook as other components for consistent query keys
  const { isSuperSafeEnabled, settings: superSafeSettings } = useSuperSafe();

  // Fetch products using the new home search endpoint (same logic as Daswos AI)
  const { data, isLoading: queryLoading, error } = useQuery<Product[]>({
    queryKey: ['/api/home-search', sphere, searchQuery, isSuperSafeEnabled, superSafeSettings],
    queryFn: async () => {
      // Use the new home search endpoint that includes Daswos AI-style deduplication
      let url = `/api/home-search?q=${encodeURIComponent(searchQuery)}&sphere=${sphere}`;

      // Add SuperSafe Mode parameters if enabled
      if (isSuperSafeEnabled) {
        url += `&superSafeEnabled=true`;
        if (superSafeSettings.blockGambling) {
          url += `&blockGambling=true`;
        }
        if (superSafeSettings.blockAdultContent) {
          url += `&blockAdultContent=true`;
        }
      }

      console.log('🔍 Home page using new search endpoint:', url);
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('Failed to fetch products');
      }
      const data = await response.json();

      console.log('🔍 Home page search results (pre-deduplicated):', {
        url,
        searchQuery,
        sphere,
        resultCount: data.length,
        sampleResults: data.slice(0, 3).map((p: any) => ({
          id: p.id,
          title: p.title,
          description: p.description?.substring(0, 100),
          tags: p.tags
        }))
      });

      // The new endpoint already handles deduplication, so we should not see duplicates
      const duplicateIds = data.filter((product: any, index: number, arr: any[]) =>
        arr.findIndex(p => p.id === product.id) !== index
      );

      if (duplicateIds.length > 0) {
        console.warn('🚨 UNEXPECTED: New endpoint returned duplicate IDs:', duplicateIds.map((p: any) => ({
          id: p.id,
          title: p.title
        })));
      }

      // Apply client-side deduplication as an additional safety net
      const uniqueProducts = Array.from(new Map(data.map(product => [product.id, product])).values());

      if (data.length !== uniqueProducts.length) {
        console.warn('🔧 Client-side deduplication removed', data.length - uniqueProducts.length, 'duplicates');
      }

      return uniqueProducts;
    },
    enabled: !!searchQuery,
    staleTime: 30000, // Cache for 30 seconds since the new endpoint is more efficient
    refetchOnMount: true,
    refetchOnWindowFocus: false, // Reduce refetching since results are pre-processed
  });

  useEffect(() => {
    if (data) {
      // The new home search endpoint already handles deduplication
      // But we'll keep a lightweight check as a safety net
      const uniqueProducts = Array.from(new Map(data.map(product => [product.id, product])).values());

      if (data.length !== uniqueProducts.length) {
        console.warn(`🚨 ShoppingResults: Backend deduplication missed ${data.length - uniqueProducts.length} duplicates`);
      } else {
        console.log(`✅ ShoppingResults: Received ${data.length} unique products from new endpoint`);
      }

      setProducts(uniqueProducts);
      setIsLoading(false);
    } else if (!queryLoading) {
      setIsLoading(false);
    }
  }, [data, queryLoading]);

  // Advanced deduplication function
  const deduplicateProducts = (products: Product[]): Product[] => {
    if (!products || products.length === 0) {
      return products;
    }

    // Step 1: Remove exact ID duplicates
    const uniqueById = Array.from(new Map(products.map(product => [product.id, product])).values());

    // Step 2: Remove near-duplicates (same title + price, keep the one with more data)
    const uniqueByTitlePrice = new Map<string, Product>();

    for (const product of uniqueById) {
      const key = `${product.title?.toLowerCase()?.trim()}_${product.price}_${product.sellerId || 'unknown'}`;
      const existing = uniqueByTitlePrice.get(key);

      if (!existing) {
        uniqueByTitlePrice.set(key, product);
      } else {
        // Keep the product with more complete data
        const existingScore = calculateProductScore(existing);
        const currentScore = calculateProductScore(product);

        if (currentScore > existingScore) {
          uniqueByTitlePrice.set(key, product);
        }
      }
    }

    return Array.from(uniqueByTitlePrice.values());
  };

  // Calculate completeness score for a product
  const calculateProductScore = (product: Product): number => {
    let score = 0;

    // Basic fields
    if (product.title) score += 10;
    if (product.description) score += 10;
    if (product.price > 0) score += 10;

    // Optional fields
    if (product.imageUrl) score += 5;
    if (product.tags && product.tags.length > 0) score += 5;
    if (product.categoryId) score += 5;

    // Quality indicators
    if (product.trustScore && product.trustScore > 0) score += 3;
    if (product.sellerVerified) score += 3;

    return score;
  };

  if (isLoading || queryLoading) {
    return (
      <div className={`w-full flex justify-center items-center py-8 ${className}`}>
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-2 text-gray-600 dark:text-gray-400">Loading products...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`w-full text-center py-8 ${className}`}>
        <p className="text-red-500">Error loading products. Please try again.</p>
      </div>
    );
  }

  if (!products || products.length === 0) {
    return (
      <div className={`w-full text-center py-8 ${className}`}>
        <p className="text-gray-600 dark:text-gray-400">No products found for "{searchQuery}".</p>
      </div>
    );
  }

  return (
    <div className={`w-full ${className}`}>
      <div className="bg-gray-100 dark:bg-gray-800 py-5 px-5 rounded-lg shadow-sm">
        <h2 className="text-base font-semibold mb-4 text-center uppercase">RESULTS FOR "{searchQuery}"</h2>
        <div className="mx-auto max-w-5xl">
          <CarouselSearchResults
            products={products}
            aiModeEnabled={aiModeEnabled}
            onCurrentProductChange={onCurrentProductChange}
          />
        </div>
      </div>
    </div>
  );
};

export default ShoppingResults;
