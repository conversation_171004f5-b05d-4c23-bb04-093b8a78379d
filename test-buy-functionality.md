# Test Buy Functionality

## Steps to Test:

1. **Open the app**: http://localhost:3003
2. **Enable Daswos AI**: Click the checkbox next to "Daswos AI" 
3. **Search for a product**: Type "carrot cake" and press Enter
4. **Select "Shopping"**: When prompted "Are you shopping?", click "Yes"
5. **Check for Buy button**: The search icon should change to a green "Buy" button
6. **Click Buy button**: Should either purchase or show coins dialog

## Expected Behavior:

### When Buy Button Should Appear:
- ✅ AI Mode is enabled (`aiModeEnabled = true`)
- ✅ Has shopping results (`hasShoppingResults = true`) 
- ✅ Selected result type is shopping (`selectedResultType = 'shopping'`)

### When Buy Button is Clicked:
- If user has enough coins (5000 initial): Purchase succeeds
- If user has insufficient coins: Purchase coins dialog appears

## Debug Console Output:

Look for these console messages:
- "Button render check:" - Shows if buy button should render
- "Search form submitted:" - Shows form submission details
- "Triggering buy action" - Confirms buy action is triggered
- "Buy button clicked, current product:" - Shows product being purchased
- "User balance: X, Product price: Y" - Shows balance vs price comparison

## Common Issues:

1. **Buy button not showing**: Check if all 3 conditions are met
2. **No popup on insufficient funds**: Check if dialog state is being set
3. **Purchase not working**: Check server logs for API errors

## Test Products:

Search for these to test different price points:
- "carrot cake" - Should find products around $10-20
- "laptop" - Should find higher priced items
- "book" - Should find lower priced items
