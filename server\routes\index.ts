import express from 'express';
import reviewsRoutes from './reviews';
import { setupAutoShopRoutes } from './autoshop';
import aiConversationRoutes from './ai-conversation';
import walletConnectionsRoutes from './wallet-connections';
import multiWalletRoutes from './multi-wallet';
import { setupRobotGuessingGameRoutes } from './robot-guessing-game';
import { storage } from '../storage';

const router = express.Router();

// Register all routes
router.use('/reviews', reviewsRoutes);
router.use('/ai-conversation', aiConversationRoutes);
router.use('/wallet', walletConnectionsRoutes);
router.use('/multi-wallet', multiWalletRoutes);

// Setup AutoShop routes
setupAutoShopRoutes(router, storage);

// Setup Robot Guessing Game routes
setupRobotGuessingGameRoutes(router, storage);

export default router;
