import { Router } from 'express';
import { IStorage } from '../../client/src/hooks/storage';

export function createOrderRoutes(storage: IStorage) {
  const router = Router();

  // Create a new order (supports both authenticated and guest users)
  router.post('/create', async (req, res) => {
    try {
      const isAuthenticated = req.isAuthenticated();
      const userId = isAuthenticated ? (req.user as any).id : null;

      const orderData = req.body;

      // Validate order data
      if (!orderData.items || !Array.isArray(orderData.items) || orderData.items.length === 0) {
        return res.status(400).json({ message: "Order must contain at least one item" });
      }

      // Validate shipping address
      if (!orderData.shippingAddress || !orderData.shippingAddress.fullName) {
        return res.status(400).json({ message: "Shipping address is required" });
      }

      // For now, require authentication for order creation
      // TODO: Add guest order support later
      if (!isAuthenticated) {
        return res.status(401).json({ message: "Authentication required for order creation" });
      }

      // Create the order
      const order = await storage.createOrder({
        userId,
        totalAmount: orderData.totalAmount,
        status: "pending",
        shippingAddress: JSON.stringify(orderData.shippingAddress),
        billingAddress: JSON.stringify(orderData.billingAddress || orderData.shippingAddress),
        paymentMethod: orderData.paymentMethod,
        paymentReference: orderData.paymentReference,
        notes: orderData.notes
      });

      // Create order items and track purchase history
      const orderItems = [];
      for (const item of orderData.items) {
        const orderItem = await storage.addOrderItem({
          orderId: order.id,
          productId: item.productId,
          quantity: item.quantity,
          priceAtPurchase: item.price,
          itemNameSnapshot: item.name,
          splitBuyId: item.splitBuyId
        });
        orderItems.push(orderItem);

        // Get product details to get category ID
        try {
          const product = await storage.getProductById(item.productId);
          if (product) {
            // Add to purchase history
            await storage.addUserPurchaseHistory(
              userId,
              item.productId,
              product.categoryId,
              item.price,
              item.quantity
            );

            // Update user preference for this category
            if (product.categoryId) {
              // Get current preference score
              const preferences = await storage.getUserProductPreferences(userId);
              const existingPreference = preferences.find(p => p.categoryId === product.categoryId);

              // Calculate new score (increase by 10 if exists, start at 10 if new)
              const newScore = existingPreference ? existingPreference.preferenceScore + 10 : 10;

              // Update preference
              await storage.updateUserProductPreference(userId, product.categoryId, newScore);
            }

            console.log(`Added purchase history for user ${userId}, product ${item.productId}, category ${product.categoryId || 'unknown'}`);
          }
        } catch (historyError) {
          console.error('Error tracking purchase history:', historyError);
          // Continue with the order creation even if history tracking fails
        }
      }

      // Clear the user's cart if requested
      // TODO: Implement clearUserCart method in storage
      // if (orderData.clearCart) {
      //   await storage.clearUserCart(userId);
      // }

      res.status(201).json({
        order,
        items: orderItems,
        orderId: order.id
      });
    } catch (error) {
      console.error('Error creating order:', error);
      res.status(500).json({ message: "Failed to create order" });
    }
  });

  // Get user's orders
  router.get('/user', async (req, res) => {
    try {
      // Check if user is authenticated
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "You must be logged in to view your orders" });
      }

      const userId = (req.user as any).id;
      const orders = await storage.getOrdersByUserId(userId);
      res.json(orders);
    } catch (error) {
      console.error('Error fetching user orders:', error);
      res.status(500).json({ message: "Failed to fetch orders" });
    }
  });

  // Get order details (supports both authenticated users and guest orders)
  router.get('/:id', async (req, res) => {
    try {
      const orderIdParam = req.params.id;

      // TODO: Add guest order support later
      // For now, all orders require authentication

      // Handle authenticated user orders
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "You must be logged in to view order details" });
      }

      const orderId = parseInt(orderIdParam);
      if (isNaN(orderId)) {
        return res.status(400).json({ message: "Invalid order ID" });
      }

      const order = await storage.getOrderById(orderId);
      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }

      // Check if the user is the owner of the order
      if (order.userId !== (req.user as any).id) {
        return res.status(403).json({ message: "You do not have permission to view this order" });
      }

      // Get order items
      const items = await storage.getOrderItemsByOrderId(orderId);

      res.json({
        order,
        items
      });
    } catch (error) {
      console.error('Error fetching order details:', error);
      res.status(500).json({ message: "Failed to fetch order details" });
    }
  });

  return router;
}
