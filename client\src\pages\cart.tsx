import React, { useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Link, useLocation } from 'wouter';
import { Package, Trash2, Plus, Minus, ShoppingBag, Home, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { DasWosCoinIcon } from '@/components/daswos-coin-icon';
import { apiRequest } from '@/lib/queryClient';
import { Badge } from '@/components/ui/badge';

interface CartItem {
  id: number;
  quantity: number;
  price: number | string;
  source?: string;
  name?: string;
  product?: {
    title?: string;
    price?: number | string;
    imageUrl?: string;
    images?: string[];
  };
}

const formatPrice = (priceInCents: number | string) => {
  const priceNum = typeof priceInCents === 'string' ? parseFloat(priceInCents) : priceInCents;
  // Convert cents to dollars for display
  const displayPrice = priceNum / 100;
  return `$${displayPrice.toFixed(2)}`;
};

const getFirstImageUrl = (product: any) => {
  if (!product) return '';
  if (product.imageUrl) return product.imageUrl;
  if (product.images && product.images.length > 0) return product.images[0];
  return '';
};

const CartPage: React.FC = () => {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch cart items
  const {
    data: cartItems = [],
    isLoading: isCartLoading,
    refetch: _refetchCart
  } = useQuery<CartItem[]>({
    queryKey: ['/api/user/cart'],
    queryFn: async () => {
      return apiRequest('/api/user/cart', {
        method: 'GET',
        credentials: 'include' // Include cookies for session consistency
      });
    },
    staleTime: 30000, // 30 seconds
  });

  // Calculate totals
  const regularItems = cartItems.filter((item) => item.source !== 'ai_shopper');
  const coinsItems = cartItems.filter((item) => item.source === 'ai_shopper');
  const totalItems = cartItems.reduce((sum, item) => sum + (item.quantity || 1), 0);

  const regularTotal = regularItems.reduce((sum, item) => {
    const itemPrice = typeof item.price === 'string' ? parseFloat(item.price) : (item.price || 0);
    // Prices are stored as dollars in the database
    return sum + (itemPrice * (item.quantity || 1));
  }, 0);

  const coinsTotal = coinsItems.reduce((sum, item) => {
    const itemPrice = typeof item.price === 'string' ? parseFloat(item.price) : (item.price || 0);
    // For DasWos coins: 1 dollar = 1 DasWos coin
    return sum + (itemPrice * (item.quantity || 1));
  }, 0);

  // Handle quantity update
  const handleUpdateQuantity = useCallback(async (itemId: number, newQuantity: number) => {
    if (newQuantity < 1) return;

    try {
      await apiRequest(`/api/user/cart/item/${itemId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ quantity: newQuantity })
      });

      await queryClient.invalidateQueries({ queryKey: ['/api/user/cart'] });

      toast({
        title: 'Cart updated',
        description: 'Item quantity has been updated.',
        duration: 2000
      });
    } catch (error) {
      console.error('Error updating quantity:', error);
      toast({
        title: 'Error',
        description: 'Could not update item quantity. Please try again.',
        variant: 'destructive',
        duration: 5000
      });
    }
  }, [queryClient, toast]);

  // Handle item removal
  const handleRemoveItem = useCallback(async (itemId: number) => {
    try {
      await apiRequest(`/api/user/cart/item/${itemId}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      await queryClient.invalidateQueries({ queryKey: ['/api/user/cart'] });

      toast({
        title: 'Item removed',
        description: 'Item has been removed from your cart.',
        duration: 2000
      });
    } catch (error) {
      console.error('Error removing item:', error);
      toast({
        title: 'Error',
        description: 'Could not remove item. Please try again.',
        variant: 'destructive',
        duration: 5000
      });
    }
  }, [queryClient, toast]);

  // Handle proceed to checkout
  const handleCheckout = () => {
    navigate('/checkout');
  };

  return (
    <div className="container mx-auto px-4 py-6 max-w-4xl">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Your Shopping Cart</h1>
        <div className="flex items-center text-sm text-gray-500">
          <Link href="/" className="flex items-center hover:text-primary">
            <Home className="h-4 w-4 mr-1" />
            <span>Home</span>
          </Link>
          <ChevronRight className="h-4 w-4 mx-2" />
          <span className="text-gray-800 font-medium">Shopping Cart</span>
        </div>
      </div>

      {isCartLoading ? (
        <div className="flex justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p>Loading your cart...</p>
          </div>
        </div>
      ) : cartItems.length === 0 ? (
        <div className="bg-white rounded-lg shadow-sm p-8 text-center border border-gray-200">
          <ShoppingBag className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Your cart is empty</h2>
          <p className="text-gray-500 mb-6">Looks like you haven't added any items to your cart yet.</p>
          <Button
            onClick={() => navigate('/search?type=shopping')}
            className="bg-primary hover:bg-primary/90 text-black font-medium"
          >
            Start Shopping
          </Button>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
          <div className="p-4 bg-gray-50 border-b">
            <h2 className="text-base font-normal">Your Shopping Cart</h2>
            <p className="text-xs text-gray-500">{totalItems} {totalItems === 1 ? 'item' : 'items'}</p>
          </div>

          <div className="max-h-[60vh] overflow-auto py-2">
            {cartItems.map((item: any) => (
              <div key={item.id} className="px-4 py-3 hover:bg-gray-50">
                <div className="flex items-start">
                  <div className="h-12 w-12 bg-gray-100 rounded flex-shrink-0 flex items-center justify-center overflow-hidden">
                    {getFirstImageUrl(item.product || item) ? (
                      <img
                        src={getFirstImageUrl(item.product || item)}
                        alt={item.name || (item.product?.title || 'Product')}
                        className="h-full w-full object-cover"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = '/placeholder-product.svg';
                        }}
                      />
                    ) : (
                      <Package className="h-6 w-6 text-gray-400" />
                    )}
                  </div>

                  <div className="ml-3 flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <h3 className="text-sm font-medium text-gray-900 truncate">
                        {item.name || item.product?.title || 'Product'}
                      </h3>
                      <button
                        onClick={() => handleRemoveItem(item.id)}
                        className="text-gray-400 hover:text-red-500"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                    
                    <div className="mt-1">
                      {item.source === 'ai_shopper' ? (
                        <div className="flex items-center">
                          <DasWosCoinIcon size={14} className="mr-1" />
                          <span className="text-sm font-medium">
                            {(item.price || item.product?.price || 0).toLocaleString()}
                          </span>
                        </div>
                      ) : (
                        <span className="text-sm font-medium">
                          ${(item.price || item.product?.price || 0).toLocaleString()}
                        </span>
                      )}
                    </div>

                    <div className="mt-2 flex items-center justify-between">
                      <div className="flex items-center border border-gray-300 rounded-md h-8">
                        <button
                          className="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 disabled:opacity-50"
                          disabled={item.quantity <= 1}
                          onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
                        >
                          <Minus className="h-3 w-3" />
                        </button>
                        <span className="w-8 text-center text-sm font-medium">
                          {item.quantity}
                        </span>
                        <button
                          className="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100"
                          onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                        >
                          <Plus className="h-3 w-3" />
                        </button>
                      </div>

                      <div className="flex space-x-1">
                        {item.source === 'ai_shopper' && (
                          <Badge className="text-[10px] px-1.5 py-0 h-5 bg-blue-100 text-blue-800 hover:bg-blue-100 border-blue-200">
                            Auto
                          </Badge>
                        )}
                        {item.source === 'ai_recommendation' && (
                          <Badge className="text-[10px] px-1.5 py-0 h-5 bg-purple-100 text-purple-800 hover:bg-purple-100 border-purple-200">
                            AI Recommended
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="border-t border-gray-300 p-4">
            {/* Regular total */}
            <div className="flex justify-between mb-1 text-sm">
              <span>Regular total:</span>
              <span>
                {regularItems.length > 0 ? `$${regularTotal.toLocaleString()}` : '$0.00'}
              </span>
            </div>

            {/* Daswos coins total */}
            {coinsItems.length > 0 && (
              <div className="flex justify-between mb-1 text-sm">
                <span>Daswos coins total:</span>
                <span className="flex items-center">
                  <DasWosCoinIcon className="mr-1" size={12} />
                  {coinsTotal.toLocaleString()}
                </span>
              </div>
            )}

            <div className="flex justify-between mb-4 text-sm text-gray-500">
              <span>Shipping</span>
              <span>Calculated at checkout</span>
            </div>

            <Button
              className="w-full bg-black text-white hover:bg-gray-800 text-sm py-2"
              onClick={handleCheckout}
            >
              PROCEED TO CHECKOUT
            </Button>

            <div className="mt-3 text-center">
              <Button
                variant="link"
                className="text-sm text-gray-600 hover:text-primary"
                onClick={() => navigate('/search?type=shopping')}
              >
                Continue Shopping
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CartPage;
