import React, { useState, useEffect, createContext, useContext } from 'react';
import { apiRequest } from '@/lib/queryClient';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/hooks/use-auth';
import { useSafeSphereContext } from '@/contexts/safe-sphere-context';
import { useSuperSafe } from '@/contexts/super-safe-context';

interface Wallet {
  id: string;
  wallet_id: string;
  // REMOVED: balance - balances are now stored in user accounts in main databases
  created_at: string;
  last_accessed: string;
}

interface WalletSession {
  wallet: Wallet;
  session_token: string;
  expires_at: string;
}

interface WalletCard {
  id: number;
  cardId: string;
  cardName?: string;
  balance: number;
  isPrimary: boolean;
  isActive: boolean;
  isSafeCard: boolean;
  totalEarned: number;
  totalSpent: number;
  createdAt: string;
}

interface WalletContextType {
  wallet: Wallet | null;
  cards: WalletCard[];
  activeCard: WalletCard | null;
  isLoading: boolean;
  loginToWallet: (walletId: string, password: string) => Promise<{ success: boolean; error?: string }>;
  createWallet: (walletId: string, password: string) => Promise<{ success: boolean; error?: string }>;
  createCard: (cardName?: string, isSafeCard?: boolean) => Promise<{ success: boolean; error?: string; card?: WalletCard }>;
  switchCard: (cardId: string) => Promise<boolean>;
  logoutWallet: () => void;
  refreshWallet: () => Promise<void>;
  refreshCards: () => Promise<void>;
  isPurchaseAllowed: (product: any) => { allowed: boolean; reason?: string };
  isWalletConnected: boolean;
  totalBalance: number;
}

const WalletContext = createContext<WalletContextType | undefined>(undefined);

export function useWallet() {
  const context = useContext(WalletContext);
  if (!context) {
    // Return a default implementation when not in context
    return useWalletImplementation();
  }
  return context;
}

function useWalletImplementation(): WalletContextType {
  const [wallet, setWallet] = useState<Wallet | null>(null);
  const [cards, setCards] = useState<WalletCard[]>([]);
  const [activeCard, setActiveCard] = useState<WalletCard | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const queryClient = useQueryClient();
  const { user, isLoading: isUserLoading } = useAuth();

  // Check for existing wallet session on mount and when user changes
  useEffect(() => {
    // Only check wallet session after user authentication has completed loading
    if (!isUserLoading) {
      checkExistingWalletSession();
    }
  }, [user, isUserLoading]); // Re-check when user login state changes or loading completes

  // Monitor active card for safe card protection (purchase blocking only)
  useEffect(() => {
    console.log('🔍 Safe card monitor effect triggered:', {
      activeCard: activeCard?.cardId,
      isSafeCard: activeCard?.isSafeCard,
      cardName: activeCard?.cardName
    });

    if (activeCard?.isSafeCard) {
      console.log('🛡️ Safe card detected - purchase protection active (will block unsafe purchases)');
    } else {
      console.log('🔓 Safe card protection deactivated');
    }
  }, [activeCard]);

  // Function to create wallet connection record
  const createWalletConnection = async (walletId: string, walletUuid: string) => {
    if (!user) return; // Only create connections for logged-in users

    // Skip connection creation for demo wallet to avoid interference
    if (walletId === 'demo-wallet') {
      console.log('Skipping connection creation for demo wallet to maintain isolation');
      return;
    }

    try {
      // Create connection record in wallet database
      const response = await fetch('/api/wallet/create-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          wallet_id: walletUuid,
          wallet_identifier: walletId,
          database_name: 'daswos-18', // This identifies which app is connecting
          user_id: user.id,
          username: user.username,
          is_primary: true // Mark as primary wallet for this user
        }),
      });

      if (!response.ok) {
        console.warn('Failed to create wallet connection record:', await response.text());
      } else {
        console.log('Wallet connection record created successfully');
      }
    } catch (error) {
      console.warn('Error creating wallet connection:', error);
    }
  };

  const checkExistingWalletSession = async () => {
    try {
      const storedWallet = localStorage.getItem('daswos_wallet');
      const storedSession = localStorage.getItem('daswos_wallet_session');

      if (storedWallet && storedSession) {
        const walletData = JSON.parse(storedWallet);
        const sessionData = JSON.parse(storedSession);

        // Check if session is still valid
        if (new Date(sessionData.expires_at) > new Date()) {
          // If user is loaded and authenticated, restore wallet session
          if (user) {
            setWallet(walletData);
            console.log('Restored wallet session:', walletData.wallet_id);
          } else if (!isUserLoading) {
            // User authentication has completed and user is not logged in
            // Clear wallet session for security
            console.log('Clearing wallet session - user not authenticated');
            localStorage.removeItem('daswos_wallet');
            localStorage.removeItem('daswos_wallet_session');
          }
          // If user is null but still loading, don't clear wallet session yet
        } else {
          // Session expired, clear data
          console.log('Wallet session expired, clearing data');
          localStorage.removeItem('daswos_wallet');
          localStorage.removeItem('daswos_wallet_session');
        }
      }
    } catch (error) {
      console.error('Error checking wallet session:', error);
      // Clear invalid data
      localStorage.removeItem('daswos_wallet');
      localStorage.removeItem('daswos_wallet_session');
    }
  };

  const loginToWallet = async (walletId: string, password: string): Promise<{ success: boolean; error?: string }> => {
    setIsLoading(true);

    try {
      // For demo purposes, check demo wallet - completely local, no database interaction
      if (walletId === 'demo-wallet' && password === 'demo123') {
        // Demo wallet - no balance stored in wallet (balance comes from user account)
        const demoWallet: Wallet = {
          id: 'demo-wallet-uuid',
          wallet_id: 'demo-wallet',
          // REMOVED: balance - balances are now stored in user accounts in main databases
          created_at: new Date().toISOString(),
          last_accessed: new Date().toISOString()
        };

        const sessionData = {
          wallet: demoWallet,
          session_token: 'demo-session-' + Date.now(),
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
        };

        // Store wallet data
        localStorage.setItem('daswos_wallet', JSON.stringify(demoWallet));
        localStorage.setItem('daswos_wallet_session', JSON.stringify(sessionData));

        setWallet(demoWallet);

        // Skip connection creation for demo wallet to maintain complete isolation
        console.log('Demo wallet login successful - completely isolated from database');

        return { success: true };
      }

      // Make actual API call to wallet database
      try {
        const response = await fetch('/api/wallet/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            walletId,
            password,
            userId: user?.id,
            username: user?.username
          })
        });

        const result = await response.json();

        if (result.success && result.wallet) {
          // Store wallet data locally
          const walletData = {
            id: result.wallet.id,
            wallet_id: result.wallet.wallet_id,
            created_at: result.wallet.created_at,
            last_accessed: result.wallet.last_accessed
          };

          const sessionData = {
            wallet: walletData,
            session_token: result.session?.token,
            expires_at: result.session?.expires_at
          };

          localStorage.setItem('daswos_wallet', JSON.stringify(walletData));
          localStorage.setItem('daswos_wallet_session', JSON.stringify(sessionData));

          setWallet(walletData);

          // Create wallet connection record for tracking
          if (user?.id) {
            await createWalletConnection(walletId, result.wallet.wallet_id);
          }

          // Refresh cards after successful login with retry mechanism
          const refreshWithRetry = async (attempts = 0) => {
            if (attempts >= 10) {
              console.error('Failed to refresh cards after 10 attempts');
              return;
            }

            // Wait for wallet state to be updated
            await new Promise(resolve => setTimeout(resolve, 200 + (attempts * 100)));

            // Check if wallet state is ready
            if (wallet?.wallet_id) {
              await refreshCards();
              console.log('✅ Cards refreshed after wallet login');
            } else {
              console.log(`⏳ Wallet state not ready, retrying... (attempt ${attempts + 1})`);
              await refreshWithRetry(attempts + 1);
            }
          };

          refreshWithRetry();

          return { success: true };
        } else {
          return { success: false, error: result.error || 'Invalid wallet ID or password' };
        }
      } catch (error) {
        console.error('Wallet login error:', error);
        return { success: false, error: 'Login failed. Please try again.' };
      }
    } finally {
      setIsLoading(false);
    }
  };

  const createWallet = async (walletId: string, password: string): Promise<{ success: boolean; error?: string }> => {
    setIsLoading(true);

    try {
      // Get current user info for the API call
      const userResponse = await fetch('/api/user', {
        credentials: 'include'
      });

      let userId, username;
      if (userResponse.ok) {
        const userData = await userResponse.json();
        userId = userData.id;
        username = userData.username;
      }

      // Call the wallet creation API
      const response = await fetch('/api/wallet/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          walletId,
          password,
          userId,
          username
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { success: false, error: errorData.error || 'Failed to create wallet' };
      }

      const result = await response.json();

      if (result.success) {
        // Store wallet data locally
        const walletData = {
          id: result.wallet.id,
          wallet_id: result.wallet.wallet_id,
          created_at: result.wallet.created_at,
          last_accessed: result.wallet.last_accessed
        };

        const sessionData = {
          wallet: walletData,
          session_token: result.session.token,
          expires_at: result.session.expires_at
        };

        localStorage.setItem('daswos_wallet', JSON.stringify(walletData));
        localStorage.setItem('daswos_wallet_session', JSON.stringify(sessionData));

        setWallet(walletData);

        // Create wallet connection record for tracking
        await createWalletConnection(walletId, result.wallet.id);

        // Refresh cards after successful wallet creation with retry mechanism
        const refreshWithRetry = async (attempts = 0) => {
          if (attempts >= 10) {
            console.error('Failed to refresh cards after wallet creation after 10 attempts');
            return;
          }

          // Wait for wallet state to be updated
          await new Promise(resolve => setTimeout(resolve, 200 + (attempts * 100)));

          // Check if wallet state is ready
          if (wallet?.wallet_id) {
            await refreshCards();
            console.log('✅ Cards refreshed after wallet creation');
          } else {
            console.log(`⏳ Wallet state not ready after creation, retrying... (attempt ${attempts + 1})`);
            await refreshWithRetry(attempts + 1);
          }
        };

        refreshWithRetry();

        return { success: true };
      } else {
        return { success: false, error: 'Failed to create wallet' };
      }

    } catch (error) {
      console.error('Wallet creation error:', error);
      return { success: false, error: 'Failed to create wallet. Please try again.' };
    } finally {
      setIsLoading(false);
    }
  };

  const logoutWallet = async () => {
    const walletId = wallet?.wallet_id;

    // Log wallet disconnect on server if user is authenticated
    if (user && walletId) {
      try {
        await apiRequest('POST', '/api/wallet/disconnect-log', { walletId });
      } catch (error) {
        console.error('Failed to log wallet disconnect:', error);
        // Continue with logout even if logging fails
      }
    }

    // Clear stored wallet data
    localStorage.removeItem('daswos_wallet');
    localStorage.removeItem('daswos_wallet_session');

    // Reset wallet state
    setWallet(null);
    setCards([]);
    setActiveCard(null);

    // Invalidate balance queries to prevent stale data
    queryClient.invalidateQueries({ queryKey: ['/api/user/daswos-coins/balance'] });

    console.log(`🔓 Wallet disconnected: ${walletId || 'unknown'} for user ${user?.username || 'unknown'} (ID: ${user?.id || 'unknown'})`);
  };

  const switchWallet = async (walletId: string): Promise<boolean> => {
    if (!user) {
      console.error('User must be authenticated to switch wallets');
      return false;
    }

    try {
      // Update the active wallet ID in the user's account
      const response = await apiRequest('/api/multi-wallet/set-active', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          userId: user.id,
          walletId: walletId
        })
      });

      if (response.success) {
        // Create a wallet object for the switched wallet
        const switchedWallet: Wallet = {
          id: `wallet-${walletId}`,
          wallet_id: walletId,
          created_at: new Date().toISOString(),
          last_accessed: new Date().toISOString()
        };

        // Update local wallet state
        setWallet(switchedWallet);

        // Store wallet data locally
        localStorage.setItem('daswos_wallet', JSON.stringify(switchedWallet));

        // Create a session for the switched wallet
        const sessionData = {
          wallet: switchedWallet,
          session_token: `session-${walletId}-${Date.now()}`,
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
        };
        localStorage.setItem('daswos_wallet_session', JSON.stringify(sessionData));

        console.log(`✅ Switched to wallet: ${walletId}`);
        return true;
      } else {
        console.error('Failed to switch wallet:', response.error);
        return false;
      }
    } catch (error) {
      console.error('Error switching wallet:', error);
      return false;
    }
  };

  const refreshWallet = async () => {
    if (!wallet) return;

    try {
      // TODO: Replace with actual API call to refresh wallet data
      // const response = await apiRequest(`/api/wallet/${wallet.id}`);
      // setWallet(response.wallet);

      console.log('Wallet refreshed');
      await refreshCards();
    } catch (error) {
      console.error('Error refreshing wallet:', error);
    }
  };

  const refreshCards = async () => {
    console.log('🔄 refreshCards called:', {
      hasWallet: !!wallet,
      hasUser: !!user,
      walletId: wallet?.wallet_id,
      userId: user?.id
    });

    if (!wallet || !user) {
      console.log('❌ refreshCards: Missing wallet or user, skipping');
      return;
    }

    try {
      console.log(`📡 Fetching cards for wallet: ${wallet.wallet_id}`);
      const response = await apiRequest(`/api/wallet/cards?walletId=${wallet.wallet_id}`, {
        method: 'GET',
        credentials: 'include'
      });

      console.log('📡 Cards API response:', response);

      if (response.success && response.cards) {
        console.log(`✅ Setting ${response.cards.length} cards:`, response.cards.map((c: any) => ({
          cardId: c.cardId,
          cardName: c.cardName,
          balance: c.balance
        })));
        setCards(response.cards);

        // Set active card based on user's preference or primary card
        const userActiveCard = response.cards.find((card: WalletCard) => card.cardId === user.activeCardId);
        const primaryCard = response.cards.find((card: WalletCard) => card.isPrimary);
        const activeCardToSet = userActiveCard || primaryCard || response.cards[0];

        console.log('🎯 Setting active card:', {
          userActiveCard: userActiveCard?.cardId,
          primaryCard: primaryCard?.cardId,
          activeCardToSet: activeCardToSet?.cardId
        });
        setActiveCard(activeCardToSet || null);
      } else {
        console.log('❌ Cards API failed or no cards:', response);
      }
    } catch (error) {
      console.error('Failed to refresh cards:', error);
      setCards([]);
      setActiveCard(null);
    }
  };

  const createCard = async (cardName?: string, isSafeCard?: boolean): Promise<{ success: boolean; error?: string; card?: WalletCard }> => {
    if (!wallet || !user) {
      return { success: false, error: 'Wallet and user authentication required' };
    }

    try {
      const response = await apiRequest('/api/wallet/cards/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          walletId: wallet.wallet_id,
          cardName: cardName || `Card ${cards.length + 1}`,
          isSafeCard: isSafeCard || false
        })
      });

      if (response.success && response.card) {
        await refreshCards();
        return { success: true, card: response.card };
      } else {
        return { success: false, error: response.error || 'Failed to create card' };
      }
    } catch (error) {
      console.error('Error creating card:', error);
      return { success: false, error: 'Network error' };
    }
  };

  const switchCard = async (cardId: string): Promise<boolean> => {
    if (!user) {
      console.error('User must be authenticated to switch cards');
      return false;
    }

    try {
      const response = await apiRequest('/api/wallet/cards/set-active', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          userId: user.id,
          cardId: cardId
        })
      });

      if (response.success) {
        const newActiveCard = cards.find(card => card.cardId === cardId);
        setActiveCard(newActiveCard || null);

        // Invalidate balance queries to refresh data
        queryClient.invalidateQueries({ queryKey: ['/api/user/daswos-coins/balance'] });

        return true;
      }
      return false;
    } catch (error) {
      console.error('Error switching card:', error);
      return false;
    }
  };

  // Check if a purchase is allowed with the current active card
  const isPurchaseAllowed = (product: any): { allowed: boolean; reason?: string } => {
    // If no active card or not a safe card, allow all purchases
    if (!activeCard?.isSafeCard) {
      return { allowed: true };
    }

    console.log('🛡️ Safe card purchase check for product:', {
      productId: product.id,
      title: product.title,
      trustScore: product.trustScore,
      identityVerified: product.identityVerified,
      tags: product.tags
    });

    // Safe card restrictions:
    // 1. Trust score must be >= 70
    if (!product.trustScore || product.trustScore < 70) {
      return {
        allowed: false,
        reason: 'Safe card protection: This product does not meet the minimum trust score requirement (70+) for safe purchases.'
      };
    }

    // 2. Seller must be identity verified
    if (!product.identityVerified || !product.identityVerificationStatus?.startsWith('app')) {
      return {
        allowed: false,
        reason: 'Safe card protection: This product is from an unverified seller. Safe cards only allow purchases from identity-verified sellers.'
      };
    }

    // 3. Block gambling-related products
    const gamblingKeywords = ['casino', 'gambling', 'poker', 'slots', 'betting', 'lottery', 'jackpot', 'roulette', 'blackjack'];
    const productText = `${product.title} ${product.description} ${product.tags?.join(' ')}`.toLowerCase();

    for (const keyword of gamblingKeywords) {
      if (productText.includes(keyword)) {
        return {
          allowed: false,
          reason: 'Safe card protection: Gambling-related products are blocked for your safety.'
        };
      }
    }

    // 4. Block adult content
    const adultKeywords = ['adult', 'porn', 'xxx', 'sex', 'erotic', 'mature', '18+', 'nsfw'];

    for (const keyword of adultKeywords) {
      if (productText.includes(keyword)) {
        return {
          allowed: false,
          reason: 'Safe card protection: Adult content is blocked for your safety.'
        };
      }
    }

    console.log('✅ Safe card purchase approved for product:', product.title);
    return { allowed: true };
  };

  const totalBalance = cards.reduce((sum, card) => sum + card.balance, 0);

  return {
    wallet,
    cards,
    activeCard,
    isLoading,
    loginToWallet,
    createWallet,
    createCard,
    switchCard,
    logoutWallet,
    refreshWallet,
    refreshCards,
    isPurchaseAllowed,
    isWalletConnected: !!wallet,
    totalBalance
  };
}

// Wallet Provider Component (for when we want to use context)
export function WalletProvider({ children }: { children: React.ReactNode }) {
  const walletImplementation = useWalletImplementation();

  return (
    <WalletContext.Provider value={walletImplementation}>
      {children}
    </WalletContext.Provider>
  );
}

// Utility functions for wallet operations
export const walletUtils = {
  // REMOVED: formatBalance - balances are now managed in user accounts, not wallets

  validateWalletId: (walletId: string): { valid: boolean; error?: string } => {
    if (!walletId || walletId.length < 3) {
      return { valid: false, error: 'Wallet ID must be at least 3 characters' };
    }
    if (walletId.length > 50) {
      return { valid: false, error: 'Wallet ID must be less than 50 characters' };
    }
    if (!/^[a-zA-Z0-9_-]+$/.test(walletId)) {
      return { valid: false, error: 'Wallet ID can only contain letters, numbers, hyphens, and underscores' };
    }
    return { valid: true };
  },

  validatePassword: (password: string): { valid: boolean; error?: string } => {
    if (!password || password.length < 6) {
      return { valid: false, error: 'Password must be at least 6 characters' };
    }
    if (password.length > 128) {
      return { valid: false, error: 'Password must be less than 128 characters' };
    }
    return { valid: true };
  }
};

export type { Wallet, WalletCard, WalletSession, WalletContextType };
