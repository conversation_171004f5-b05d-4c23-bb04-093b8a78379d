import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { useAdminSettings } from '@/hooks/use-admin-settings';

interface SafeSphereContextType {
  isSafeSphere: boolean;
  setIsSafeSphere: (active: boolean) => void;
  toggleSafeSphere: () => void;
  isLoading: boolean;
  isLocked: boolean; // True when safe card protection is active
  setIsLocked: (locked: boolean) => void; // Method to set locked state
}

const SafeSphereContext = createContext<SafeSphereContextType>({
  isSafeSphere: true,
  setIsSafeSphere: () => {},
  toggleSafeSphere: () => {},
  isLoading: false,
  isLocked: false,
  setIsLocked: () => {}
});

export const useSafeSphereContext = () => useContext(SafeSphereContext);

interface SafeSphereProviderProps {
  children: ReactNode;
}

export const SafeSphereProvider: React.FC<SafeSphereProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const { settings } = useAdminSettings();

  // Initialize from sessionStorage if available, otherwise default to true
  const [isSafeSphere, setIsSafeSphere] = useState(() => {
    try {
      const stored = sessionStorage.getItem('safesphere-enabled');
      return stored !== null ? JSON.parse(stored) : true;
    } catch {
      return true;
    }
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isLocked, setIsLocked] = useState(false);

  // Fetch the user's SafeSphere preference when logged in
  useEffect(() => {
    const fetchSafeSphereStatus = async () => {
      if (!user) {
        // For non-authenticated users, default to SafeSphere enabled
        setIsSafeSphere(true);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const response = await fetch('/api/user/safesphere');
        if (!response.ok) {
          console.error('Failed to fetch SafeSphere status');
          // Default to SafeSphere enabled on error
          setIsSafeSphere(true);
          return;
        }

        const data = await response.json();
        const serverPreference = data.active;

        console.log(`🛡️ SafeSphere context: Fetched server data: ${serverPreference}`);

        // Check if we have session data that might be more recent
        const sessionPreference = sessionStorage.getItem('safesphere-enabled');

        if (sessionPreference !== null) {
          // Use session data if available (more recent user interaction)
          const parsedPreference = JSON.parse(sessionPreference);
          console.log(`💾 SafeSphere context: Using session data: ${parsedPreference}`);
          setIsSafeSphere(parsedPreference);
        } else {
          // Use server data if no session data
          console.log(`🌐 SafeSphere context: Using server data: ${serverPreference}`);
          setIsSafeSphere(serverPreference);
        }

      } catch (error) {
        console.error('Error fetching SafeSphere status:', error);
        // Default to SafeSphere enabled on error
        setIsSafeSphere(true);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSafeSphereStatus();
  }, [user]);

  // Save the user's SafeSphere preference
  const saveSafeSpherePreference = async (active: boolean) => {
    // Always save to sessionStorage for immediate persistence
    try {
      sessionStorage.setItem('safesphere-enabled', JSON.stringify(active));
      console.log(`💾 SafeSphere preference saved to session: ${active}`);
    } catch (error) {
      console.error('Error saving SafeSphere to sessionStorage:', error);
    }

    // Also save to server if user is authenticated
    if (!user && !settings.paidFeaturesDisabled) return;

    try {
      const response = await fetch('/api/user/safesphere', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ active })
      });

      if (!response.ok) {
        console.error('Failed to update SafeSphere status');
      } else {
        console.log(`🛡️ SafeSphere preference saved to server: ${active}`);
      }
    } catch (error) {
      console.error('Error updating SafeSphere status:', error);
    }
  };

  const toggleSafeSphere = () => {
    // Prevent toggling if locked (safe card protection active)
    if (isLocked) {
      console.log('🔒 SafeSphere toggle blocked - safe card protection is active');
      return;
    }

    const newValue = !isSafeSphere;
    setIsSafeSphere(newValue);
    saveSafeSpherePreference(newValue);
  };

  const updateSafeSphere = (value: boolean) => {
    console.log(`🔧 SafeSphere updateSafeSphere called: value=${value}, isLocked=${isLocked}, current=${isSafeSphere}`);

    // Prevent manual updates if locked, unless enabling SafeSphere
    if (isLocked && !value) {
      console.log('🔒 SafeSphere disable blocked - safe card protection is active');
      return;
    }

    console.log(`🔧 SafeSphere setting state to: ${value}`);
    setIsSafeSphere(value);
    saveSafeSpherePreference(value);
  };

  const updateIsLocked = (locked: boolean) => {
    console.log(`🔒 SafeSphere setIsLocked called: locked=${locked}, current=${isLocked}`);
    setIsLocked(locked);
  };

  return (
    <SafeSphereContext.Provider
      value={{
        isSafeSphere,
        setIsSafeSphere: updateSafeSphere,
        toggleSafeSphere,
        isLoading,
        isLocked,
        setIsLocked: updateIsLocked
      }}
    >
      {children}
    </SafeSphereContext.Provider>
  );
};

export default SafeSphereProvider;