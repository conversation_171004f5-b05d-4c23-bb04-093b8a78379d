import React, { useEffect, useRef, useCallback, useImperativeHandle, forwardRef } from 'react';
import Sketch from 'react-p5';
import p5Types from 'p5';
import { useRobotAnimation, RobotState } from '@/hooks/use-robot-animation';

interface RobotAnimationProps {
  className?: string;
  onRobotStateChange?: (state: RobotState) => void;
  isVisible?: boolean;
  isGlidingAway?: boolean;
  isFullScreenMode?: boolean;
  showGuessResult?: boolean;
  onScreenClick?: (event: React.MouseEvent) => void;
  onScreenDoubleClick?: (event: React.MouseEvent) => void;
  onScreenContextMenu?: (event: React.MouseEvent) => void;
  onScreenMouseUp?: (event: React.MouseEvent) => void;
  onScreenMouseDown?: (event: React.MouseEvent) => void;
  onRightClickLeftClick?: (event: React.MouseEvent) => void;
}

export interface RobotAnimationRef {
  setRobotState: (state: RobotState) => void;
  setRobotScale: (scale: number) => void;
  centerRobot: () => void;
  rollToPosition: (x: number, y: number) => void;
  glideAway: () => void;
  setInitialPosition: (x: number, y: number) => void;
  setPosition: (x: number, y: number) => void;
}

// Constants
const TRANSITION_DURATION = 500;
const VIEW_TRANSITION_DURATION = 300;
const SHADOW_OPACITY = 0.3;
const SHADOW_SCALE_Y = 0.2;
const SHADOW_OFFSET_Y = 20;
const HOLD_THRESHOLD = 300;
const MAX_SPIN_SPEED = 0.15;
const SCALE_TRANSITION_SPEED = 0.1;
const POSITION_TRANSITION_SPEED = 0.05;

const RobotAnimation = forwardRef<RobotAnimationRef, RobotAnimationProps>(({
  className = '',
  onRobotStateChange,
  isVisible = true,
  isGlidingAway = false,
  isFullScreenMode = false,
  showGuessResult = false,
  onScreenClick,
  onScreenDoubleClick,
  onScreenContextMenu,
  onScreenMouseUp,
  onScreenMouseDown,
  onRightClickLeftClick
}, ref) => {
  const {
    state,
    updateState,
    setRobotState,
    setRobotScale,
    centerRobot,
    rollToPosition,
    initializePosition,
    setPosition,
  } = useRobotAnimation();

  const robotImagesRef = useRef<{
    idle: p5Types.Image | null;
    talk: p5Types.Image | null;
    dance: p5Types.Image | null;
    search: p5Types.Image | null;
    roll: p5Types.Image | null;
    sing: p5Types.Image | null;
    walk: p5Types.Image | null;
    glamour: p5Types.Image | null;
    back: p5Types.Image | null;
    trainers: p5Types.Image | null;
  }>({
    idle: null,
    talk: null,
    dance: null,
    search: null,
    roll: null,
    sing: null,
    walk: null,
    glamour: null,
    back: null,
    trainers: null,
  });

  const p5InstanceRef = useRef<p5Types | null>(null);

  // Helper function to check if mouse is over UI elements
  const isMouseOverUI = useCallback((p5: p5Types) => {
    // In full screen mode, only check for robot controls and buttons (header and search are hidden)
    if (isFullScreenMode) {
      // Robot controls area
      const controlsLeft = 16;
      const controlsRight = 216;
      const controlsTop = (p5.height / 2) - 200;
      const controlsBottom = (p5.height / 2) + 200;

      // Go Away button area
      const goAwayButtonLeft = 16;
      const goAwayButtonRight = 150;
      const goAwayButtonTop = p5.height - 70;
      const goAwayButtonBottom = p5.height - 16;

      // Check if mouse is over robot controls
      if (p5.mouseX >= controlsLeft && p5.mouseX <= controlsRight &&
          p5.mouseY >= controlsTop && p5.mouseY <= controlsBottom) {
        return true;
      }

      // Check if mouse is over go away button
      if (p5.mouseX >= goAwayButtonLeft && p5.mouseX <= goAwayButtonRight &&
          p5.mouseY >= goAwayButtonTop && p5.mouseY <= goAwayButtonBottom) {
        return true;
      }

      return false;
    }

    // In compact mode, check for all UI elements
    // Header area (top of screen)
    const headerHeight = 60;
    if (p5.mouseY <= headerHeight) {
      return true;
    }

    // Search bar area (center-top area where search interface is located)
    const searchAreaTop = headerHeight;
    const searchAreaBottom = p5.height * 0.4; // Top 40% of screen below header
    const searchAreaLeft = p5.width * 0.1;    // 10% margin from left
    const searchAreaRight = p5.width * 0.9;   // 10% margin from right

    if (p5.mouseX >= searchAreaLeft && p5.mouseX <= searchAreaRight &&
        p5.mouseY >= searchAreaTop && p5.mouseY <= searchAreaBottom) {
      return true;
    }

    // Bottom search bar area (for robot fullscreen mode)
    const bottomSearchAreaLeft = p5.width * 0.25;   // 25% from left
    const bottomSearchAreaRight = p5.width * 0.75;  // 75% from left
    const bottomSearchAreaTop = p5.height - 100;    // 100px from bottom
    const bottomSearchAreaBottom = p5.height - 20;  // 20px from bottom

    if (p5.mouseX >= bottomSearchAreaLeft && p5.mouseX <= bottomSearchAreaRight &&
        p5.mouseY >= bottomSearchAreaTop && p5.mouseY <= bottomSearchAreaBottom) {
      return true;
    }

    // Robot controls area
    const controlsLeft = 16;
    const controlsRight = 216;
    const controlsTop = (p5.height / 2) - 200;
    const controlsBottom = (p5.height / 2) + 200;

    // Go Away button area
    const goAwayButtonLeft = 16;
    const goAwayButtonRight = 150;
    const goAwayButtonTop = p5.height - 70;
    const goAwayButtonBottom = p5.height - 16;

    // Daswos button area (bottom-left)
    const daswosButtonLeft = 16;
    const daswosButtonRight = 150;
    const daswosButtonTop = p5.height - 120;
    const daswosButtonBottom = p5.height - 80;

    // Check if mouse is over robot controls
    if (p5.mouseX >= controlsLeft && p5.mouseX <= controlsRight &&
        p5.mouseY >= controlsTop && p5.mouseY <= controlsBottom) {
      return true;
    }

    // Check if mouse is over go away button
    if (p5.mouseX >= goAwayButtonLeft && p5.mouseX <= goAwayButtonRight &&
        p5.mouseY >= goAwayButtonTop && p5.mouseY <= goAwayButtonBottom) {
      return true;
    }

    // Check if mouse is over daswos button area
    if (p5.mouseX >= daswosButtonLeft && p5.mouseX <= daswosButtonRight &&
        p5.mouseY >= daswosButtonTop && p5.mouseY <= daswosButtonBottom) {
      return true;
    }

    return false;
  }, [isFullScreenMode]);

  // Glide away function
  const glideAway = useCallback(() => {
    if (p5InstanceRef.current) {
      const p5 = p5InstanceRef.current;
      // Set target position to off-screen right
      const targetX = p5.width + 200; // Off-screen to the right
      const targetY = state.robotY; // Keep same Y position

      // Set robot to side view for gliding, but keep it in idle state (no rolling)
      updateState({
        targetView: 'side',
        currentView: 'side',
        targetX,
        targetY,
        isRolling: false, // Don't roll - just glide smoothly
      });

      // Keep robot in idle state for the gliding animation
      setRobotState('idle');
    }
  }, [state.robotY, updateState, setRobotState]);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    setRobotState,
    setRobotScale,
    centerRobot,
    rollToPosition,
    glideAway,
    setInitialPosition: (x: number, y: number) => {
      // Use initializePosition to set both current position and center
      if (initializePosition) {
        initializePosition(x, y);
      }
    },
    setPosition: (x: number, y: number) => {
      // Use setPosition to move robot without changing center
      if (setPosition) {
        setPosition(x, y);
      }
    },
  }), [setRobotState, setRobotScale, centerRobot, rollToPosition, glideAway, initializePosition, setPosition]);

  // Cleanup effect to prevent memory leaks and errors
  useEffect(() => {
    return () => {
      // Clear the p5 instance reference when component unmounts
      if (p5InstanceRef.current) {
        console.log('🧹 Cleaning up p5 instance reference');
        p5InstanceRef.current = null;
      }
    };
  }, []);

  // Notify parent of state changes
  useEffect(() => {
    if (onRobotStateChange) {
      onRobotStateChange(state.robotState);
    }
  }, [state.robotState, onRobotStateChange]);

  const preload = useCallback((p5: p5Types) => {
    // Load robot images for different states (all PNG with transparent backgrounds)
    robotImagesRef.current.idle = p5.loadImage('/assets/robot/daswos_redesign_correct_logo_simple.png');
    robotImagesRef.current.talk = p5.loadImage('/assets/robot/daswos_redesign_complete_teeth.png');
    robotImagesRef.current.dance = p5.loadImage('/assets/robot/daswos_dancing_pose.png');
    robotImagesRef.current.search = p5.loadImage('/assets/robot/daswos_action_pose.png');
    robotImagesRef.current.roll = p5.loadImage('/assets/robot/daswos_redesign_side.png');
    robotImagesRef.current.sing = p5.loadImage('/assets/robot/daswos_singing_pose.png');
    robotImagesRef.current.walk = p5.loadImage('/assets/robot/daswos_walking_pose.png');
    robotImagesRef.current.glamour = p5.loadImage('/assets/robot/daswos_redesign_correct_logo_lipstick.png');
    robotImagesRef.current.back = p5.loadImage('/assets/robot/daswos_back_view.png');
    robotImagesRef.current.trainers = p5.loadImage('/assets/robot/daswos_back_generic_trainers.png');
  }, []);

  const setup = useCallback((p5: p5Types, canvasParentRef: Element) => {
    p5InstanceRef.current = p5;

    // Safety check: Ensure canvasParentRef is valid
    if (!canvasParentRef) {
      console.warn('⚠️ Canvas parent ref is null, cannot create canvas');
      return;
    }

    // Create canvas that fills the window
    p5.createCanvas(p5.windowWidth, p5.windowHeight).parent(canvasParentRef);

    // DISABLED: Don't auto-initialize position - let parent component control positioning
    // Initialize robot position in compact mode (bottom-right corner)
    // const compactX = p5.width * 0.92; // 92% from left (further right to avoid nav buttons)
    // const compactY = p5.height * 0.82; // 82% from top (lower to avoid nav buttons)
    // initializePosition(compactX, compactY);

    // Start in idle state (no entrance animation)
    setRobotState('idle');
  }, [setRobotState]);

  const updateAnimation = useCallback((p5: p5Types) => {
    const currentTime = p5.millis();
    const timeInState = currentTime - state.stateStartTime;

    // If gliding away, handle special gliding animation
    if (isGlidingAway) {
      // Force side view and smooth gliding movement (no rolling)
      const dx = state.targetX - state.robotX;
      const dy = state.targetY - state.robotY;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (distance > 5) {
        const glideSpeed = 8; // Fast speed for gliding away
        const newX = state.robotX + Math.cos(Math.atan2(dy, dx)) * glideSpeed;
        const newY = state.robotY + Math.sin(Math.atan2(dy, dx)) * glideSpeed;

        updateState({
          robotX: newX,
          robotY: newY,
          targetView: 'side',
          currentView: 'side',
          isRolling: false, // No rolling - just smooth gliding
          bodyRotation: 0, // Keep robot upright
          bodyRotationSpeed: 0,
        });
      }
      return; // Skip normal animation logic when gliding away
    }

    // Handle smooth scale transitions
    if (Math.abs(state.robotScale - state.targetScale) > 0.01) {
      const newScale = p5.lerp(state.robotScale, state.targetScale, SCALE_TRANSITION_SPEED);
      updateState({ robotScale: newScale });
    }

    // DISABLED: Automatic centering for guessing game
    // Handle smooth return to center when needed - DISABLED for guessing game
    // if (state.shouldReturnToCenter && !state.isRolling) {
    //   const distanceToCenter = p5.dist(state.robotX, state.robotY, state.centerX, state.centerY);
    //   if (distanceToCenter > 2) {
    //     const newX = p5.lerp(state.robotX, state.centerX, POSITION_TRANSITION_SPEED);
    //     const newY = p5.lerp(state.robotY, state.centerY, POSITION_TRANSITION_SPEED);
    //     updateState({ robotX: newX, robotY: newY });
    //   } else {
    //     updateState({
    //       robotX: state.centerX,
    //       robotY: state.centerY,
    //       shouldReturnToCenter: false,
    //     });
    //   }
    // }

    // Handle mouse hold spinning
    if (state.isMousePressed) {
      const holdDuration = currentTime - state.mouseHoldStartTime;
      if (holdDuration > HOLD_THRESHOLD && !state.isSpinning) {
        updateState({ isSpinning: true, spinSpeed: 0 });
      }

      if (state.isSpinning) {
        const newSpinSpeed = Math.min(state.spinSpeed + 0.005, MAX_SPIN_SPEED);
        const newSpinRotation = state.spinRotation + newSpinSpeed;
        updateState({ spinSpeed: newSpinSpeed, spinRotation: newSpinRotation });
      }
    } else if (state.isSpinning) {
      const newSpinSpeed = Math.max(state.spinSpeed - 0.01, 0);
      const newSpinRotation = state.spinRotation + newSpinSpeed;

      if (newSpinSpeed <= 0) {
        updateState({ isSpinning: false, spinSpeed: 0, spinRotation: 0 });
      } else {
        updateState({ spinSpeed: newSpinSpeed, spinRotation: newSpinRotation });
      }
    }

    // Handle rolling movement
    if (state.isRolling) {
      const dx = state.targetX - state.robotX;
      const dy = state.targetY - state.robotY;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (distance < 5) {
        updateState({ isRolling: false });
        if (state.robotState === 'roll') {
          setRobotState('idle');
        }
      } else {
        const rollDirection = Math.atan2(dy, dx);
        const rollSpeed = Math.min(distance * 0.05, 5);
        const newX = state.robotX + Math.cos(rollDirection) * rollSpeed;
        const newY = state.robotY + Math.sin(rollDirection) * rollSpeed;

        const bodyRotationSpeed = rollSpeed * 0.2;
        const newBodyRotation = state.bodyRotation + bodyRotationSpeed;

        // Set appropriate view based on roll direction
        let targetView = state.targetView;
        if (Math.abs(Math.cos(rollDirection)) > Math.abs(Math.sin(rollDirection))) {
          targetView = 'side';
        } else {
          targetView = Math.sin(rollDirection) > 0 ? 'threeQuarter' : 'back';
        }

        updateState({
          robotX: newX,
          robotY: newY,
          rollDirection,
          rollSpeed,
          bodyRotation: newBodyRotation,
          bodyRotationSpeed,
          targetView,
          legsVisible: false,
          legsVisibility: Math.max(0, state.legsVisibility - 0.1),
        });
      }
    } else if (state.robotState !== 'roll' && !state.legsVisible) {
      updateState({
        legsVisible: true,
        legsVisibility: Math.min(1, state.legsVisibility + 0.05),
      });
    }

    // Handle view transitions
    if (state.currentView !== state.targetView) {
      const newProgress = Math.min(1, timeInState / VIEW_TRANSITION_DURATION);
      if (newProgress >= 1) {
        updateState({
          currentView: state.targetView,
          viewTransitionProgress: 0,
        });
      } else {
        updateState({ viewTransitionProgress: newProgress });
      }
    }

    // State-specific updates
    let stateUpdates: any = {};

    switch (state.robotState) {
      case 'idle':
        // Add bouncing effect when showing guess results
        const bounceMultiplier = showGuessResult ? 3 : 1; // 3x more bouncing when showing results
        const bounceSpeed = showGuessResult ? 0.008 : 0.002; // Faster bouncing when showing results

        stateUpdates = {
          headBobAmount: Math.sin(currentTime * bounceSpeed) * (5 * bounceMultiplier),
          headRotation: Math.sin(currentTime * 0.001) * 0.1,
          armLeftRotation: Math.sin(currentTime * 0.001) * 0.05,
          armRightRotation: Math.sin(currentTime * 0.001 + Math.PI) * 0.05,
          targetView: state.isRolling ? state.targetView : 'front',
        };

        // Handle eye blinking
        if (currentTime > state.eyeBlinkTime && !state.isBlinking) {
          stateUpdates.isBlinking = true;
          stateUpdates.eyeBlinkTime = currentTime + 200;
        } else if (currentTime > state.eyeBlinkTime && state.isBlinking) {
          stateUpdates.isBlinking = false;
          stateUpdates.eyeBlinkTime = currentTime + p5.random(2000, 5000);
        }
        break;

      case 'talk':
        const newMouthAnimation = state.mouthAnimation + 0.15;
        stateUpdates = {
          talkPulse: Math.sin(currentTime * 0.01) * 0.05,
          headBobAmount: Math.sin(currentTime * 0.01) * 3,
          armLeftRotation: Math.sin(currentTime * 0.008) * 0.2,
          armRightRotation: Math.sin(currentTime * 0.008 + Math.PI) * 0.2,
          targetView: 'front',
          mouthAnimation: newMouthAnimation,
          mouthOpenAmount: Math.abs(Math.sin(newMouthAnimation)) * 0.8, // Mouth opens and closes
        };
        break;

      case 'dance':
        const newDancePhase = state.dancePhase + 0.05;
        stateUpdates = {
          dancePhase: newDancePhase,
          headBobAmount: Math.sin(newDancePhase * 2) * 8,
          headRotation: Math.sin(newDancePhase) * 0.2,
          armLeftRotation: Math.sin(newDancePhase) * 0.4,
          armRightRotation: Math.sin(newDancePhase + Math.PI) * 0.4,
        };

        if (!state.isRolling) {
          stateUpdates.robotX = state.danceStartX + Math.sin(newDancePhase) * 30;
          stateUpdates.robotY = state.danceStartY + Math.sin(newDancePhase * 0.5) * 10;
        }

        // Cycle through views for dancing
        if (timeInState % 2000 < 500) {
          stateUpdates.targetView = 'front';
        } else if (timeInState % 2000 < 1000) {
          stateUpdates.targetView = 'threeQuarter';
        } else if (timeInState % 2000 < 1500) {
          stateUpdates.targetView = 'side';
        } else {
          stateUpdates.targetView = 'threeQuarter';
        }
        break;

      case 'search':
        const newSearchAngle = state.searchAngle + 0.03;
        stateUpdates = {
          searchAngle: newSearchAngle,
          headRotation: Math.sin(newSearchAngle) * 0.3,
          headBobAmount: Math.sin(currentTime * 0.005) * 3,
          armLeftRotation: Math.sin(newSearchAngle * 0.5) * 0.2,
          armRightRotation: Math.sin(newSearchAngle * 0.5 + Math.PI) * 0.2,
        };

        // Cycle through views for searching
        if (timeInState % 3000 < 1000) {
          stateUpdates.targetView = 'front';
        } else if (timeInState % 3000 < 2000) {
          stateUpdates.targetView = 'threeQuarter';
        } else {
          stateUpdates.targetView = 'side';
        }
        break;

      case 'roll':
        stateUpdates = {
          headBobAmount: Math.sin(currentTime * 0.01) * 3,
          armLeftRotation: Math.sin(currentTime * 0.01) * 0.1 * (state.rollSpeed * 0.1),
          armRightRotation: Math.sin(currentTime * 0.01 + Math.PI) * 0.1 * (state.rollSpeed * 0.1),
        };
        break;

      case 'sing':
        const newSingAnimation = state.mouthAnimation + 0.2;
        stateUpdates = {
          talkPulse: Math.sin(currentTime * 0.015) * 0.08, // More dramatic pulsing for singing
          headBobAmount: Math.sin(currentTime * 0.008) * 6, // More head movement
          armLeftRotation: Math.sin(currentTime * 0.01) * 0.3,
          armRightRotation: Math.sin(currentTime * 0.01 + Math.PI) * 0.3,
          mouthAnimation: newSingAnimation,
          mouthOpenAmount: Math.abs(Math.sin(newSingAnimation)) * 1.0, // Wider mouth opening
        };
        break;

      case 'walk':
        stateUpdates = {
          headBobAmount: Math.sin(currentTime * 0.01) * 8, // Walking bounce
          armLeftRotation: Math.sin(currentTime * 0.01) * 0.3,
          armRightRotation: Math.sin(currentTime * 0.01 + Math.PI) * 0.3,
        };
        break;

      case 'glamour':
        stateUpdates = {
          headBobAmount: Math.sin(currentTime * 0.003) * 2, // Subtle, elegant movement
          headRotation: Math.sin(currentTime * 0.002) * 0.05,
        };
        break;

      case 'back':
        stateUpdates = {
          headBobAmount: Math.sin(currentTime * 0.002) * 3,
        };
        break;

      case 'trainers':
        stateUpdates = {
          headBobAmount: Math.sin(currentTime * 0.004) * 4, // Sporty bounce
          armLeftRotation: Math.sin(currentTime * 0.003) * 0.1,
          armRightRotation: Math.sin(currentTime * 0.003 + Math.PI) * 0.1,
        };
        break;
    }

    if (Object.keys(stateUpdates).length > 0) {
      updateState(stateUpdates);
    }
  }, [state, updateState, setRobotState, isGlidingAway]);

  const drawShadow = useCallback((p5: p5Types) => {
    p5.push();
    p5.translate(0, SHADOW_OFFSET_Y);
    p5.fill(0, 0, 0, SHADOW_OPACITY * 255);
    p5.noStroke();
    p5.ellipse(0, 0, 120 * state.robotScale, 30 * state.robotScale * SHADOW_SCALE_Y);
    p5.pop();
  }, [state.robotScale]);

  const drawRobot = useCallback((p5: p5Types) => {
    p5.push();
    p5.translate(state.robotX, state.robotY);

    // Draw shadow
    drawShadow(p5);

    // Apply bobbing effect
    p5.translate(0, state.headBobAmount);

    // Scale the robot
    p5.scale(state.robotScale);

    // Apply spin rotation if spinning
    if (state.isSpinning || state.spinSpeed > 0) {
      p5.rotate(state.spinRotation);
    }

    // Apply body rotation for wheel effect (only when actually rolling, not when gliding away)
    if (state.robotState === 'roll' && !isGlidingAway) {
      p5.push();
      p5.rotate(state.bodyRotation);
    }

    // Determine which image to draw based on current robot state
    const currentImage = robotImagesRef.current[state.robotState];

    // Apply effects based on state
    if (state.robotState === 'talk' || state.robotState === 'sing') {
      // Subtle pulsing for talking/singing
      p5.scale(1 + state.talkPulse);
    }

    // Draw the robot image for current state
    if (currentImage) {
      p5.image(currentImage, -currentImage.width / 2, -currentImage.height / 2);
    }

    if (state.robotState === 'roll' && !isGlidingAway) {
      p5.pop(); // End body rotation
    }

    p5.pop();
  }, [state, drawShadow]);

  const handleMouseInteraction = useCallback((p5: p5Types) => {
    // Don't handle mouse interactions when gliding away
    if (isGlidingAway) {
      return;
    }

    // Only handle mouse interactions in full screen mode
    if (!isFullScreenMode) {
      return;
    }

    // Don't handle mouse interactions when cursor is over UI elements
    if (isMouseOverUI(p5)) {
      return;
    }

    // Calculate distance from mouse to robot
    const d = p5.dist(p5.mouseX, p5.mouseY, state.robotX, state.robotY);

    // If mouse is near robot and moving (scale interaction range with robot size)
    if (d < 150 * state.robotScale &&
        (Math.abs(p5.mouseX - state.lastMouseX) > 5 || Math.abs(p5.mouseY - state.lastMouseY) > 5)) {

      // Make robot look toward mouse
      const angle = Math.atan2(p5.mouseY - state.robotY, p5.mouseX - state.robotX);
      const newHeadRotation = p5.lerp(state.headRotation, angle * 0.2, 0.1);

      // Set appropriate view based on mouse position
      let targetView = state.targetView;
      if (Math.abs(Math.cos(angle)) > 0.7) {
        // Mouse is more to the sides
        targetView = 'threeQuarter';
      } else {
        // Mouse is more above/below
        targetView = Math.sin(angle) > 0 ? 'front' : 'top';
      }

      updateState({
        headRotation: newHeadRotation,
        targetView,
        mouseInteractionTimer: p5.millis() + 1000,
      });
    }

    // If interaction timer expired, return to default view for current state
    if (state.mouseInteractionTimer > 0 && p5.millis() > state.mouseInteractionTimer) {
      let defaultView = 'front';
      switch (state.robotState) {
        case 'idle':
        case 'talk':
          defaultView = 'front';
          break;
        case 'dance':
        case 'search':
        case 'roll':
          // Keep current view for these states
          return;
      }

      updateState({
        mouseInteractionTimer: 0,
        targetView: defaultView,
      });
    }

    // Store current mouse position for next frame
    updateState({
      lastMouseX: p5.mouseX,
      lastMouseY: p5.mouseY,
    });
  }, [state, updateState, isGlidingAway, isMouseOverUI, isFullScreenMode]);

  const draw = useCallback((p5: p5Types) => {
    // Clear background - make it transparent
    p5.clear();

    // Update animation
    updateAnimation(p5);

    // Draw robot
    drawRobot(p5);

    // Handle mouse interaction
    handleMouseInteraction(p5);
  }, [updateAnimation, drawRobot, handleMouseInteraction]);

  const mousePressed = useCallback((p5: p5Types) => {
    // Safety check: Don't handle mouse events if component is being destroyed or gliding away
    if (!p5 || !p5InstanceRef.current || isGlidingAway || !isVisible) {
      return;
    }

    // Additional safety check for p5 methods
    if (!p5.millis || typeof p5.millis !== 'function' || !p5.dist || typeof p5.dist !== 'function') {
      console.warn('p5 instance is not properly initialized');
      return;
    }

    // Don't handle mouse press when cursor is over UI elements
    if (isMouseOverUI && typeof isMouseOverUI === 'function' && isMouseOverUI(p5)) {
      return;
    }

    try {
      const d = p5.dist(p5.mouseX, p5.mouseY, state.robotX, state.robotY);

      updateState({
        isMousePressed: true,
        mouseHoldStartTime: p5.millis(),
      });

      // Handle screen click for guessing game (delegate to parent)
      if (onScreenClick && isFullScreenMode) {
        const mockEvent = {
          clientX: p5.mouseX,
          clientY: p5.mouseY,
          currentTarget: {
            getBoundingClientRect: () => ({ left: 0, top: 0 })
          },
          preventDefault: () => {},
          button: p5.mouseButton === 'left' ? 0 : (p5.mouseButton === 'right' ? 2 : 1)
        } as any;
        onScreenClick(mockEvent);
      }

      // Handle right-click + left-click combination
      if (onRightClickLeftClick && isFullScreenMode && p5.mouseButton === 'left') {
        const mockEvent = {
          clientX: p5.mouseX,
          clientY: p5.mouseY,
          button: 0,
          preventDefault: () => {}
        } as any;
        onRightClickLeftClick(mockEvent);
      }

      if (d < 100 * state.robotScale) {
        // Quick reaction animation (will trigger if not held long enough)
        setTimeout(() => {
          if (!state.isSpinning && !state.isMousePressed) {
            updateState({ headBobAmount: -10 });
            setTimeout(() => {
              updateState({ headBobAmount: 0 });
            }, 300);
          }
        }, HOLD_THRESHOLD + 50);
      }
    } catch (error) {
      console.warn('Error in mousePressed handler:', error);
    }
  }, [state, updateState, isMouseOverUI, isGlidingAway, isVisible, onScreenClick, onRightClickLeftClick, isFullScreenMode]);

  const mouseReleased = useCallback((p5: p5Types) => {
    // Safety check: Don't handle mouse events if component is being destroyed or gliding away
    if (!p5 || !p5InstanceRef.current || isGlidingAway || !isVisible) {
      return;
    }

    // Additional safety check for p5 methods
    if (!p5.millis || typeof p5.millis !== 'function') {
      console.warn('p5 instance is not properly initialized');
      return;
    }

    try {
      const holdDuration = p5.millis() - state.mouseHoldStartTime;

      updateState({ isMousePressed: false });

      // Don't handle mouse release when cursor is over UI elements
      if (isMouseOverUI && typeof isMouseOverUI === 'function' && isMouseOverUI(p5)) {
        return;
      }

      // REMOVED: Robot no longer moves to cursor clicks
      // Robot only spins when held down, no other click behavior
      console.log('🤖 Mouse released - robot only spins on hold, no cursor following');
    } catch (error) {
      console.warn('Error in mouseReleased handler:', error);
    }
  }, [state, updateState, isMouseOverUI, isGlidingAway, isVisible]);

  const windowResized = useCallback((p5: p5Types) => {
    // Safety check: Ensure p5 instance is valid
    if (!p5 || !p5.resizeCanvas) {
      console.warn('⚠️ p5 instance is invalid in windowResized');
      return;
    }

    p5.resizeCanvas(p5.windowWidth, p5.windowHeight);

    // DISABLED: Don't auto-update center position for guessing game
    // Update center position
    // const newCenterX = p5.width / 2;
    // const newCenterY = p5.height / 2;
    // updateState({
    //   centerX: newCenterX,
    //   centerY: newCenterY,
    // });
  }, [updateState]);

  // Create safe event handlers that won't be undefined (MUST be before early return)
  const safeMousePressed = useCallback((p5: p5Types) => {
    if (isFullScreenMode && !isGlidingAway && isVisible) {
      mousePressed(p5);
    }
  }, [isFullScreenMode, isGlidingAway, isVisible, mousePressed]);

  const safeMouseReleased = useCallback((p5: p5Types) => {
    if (isFullScreenMode && !isGlidingAway && isVisible) {
      mouseReleased(p5);
    }
  }, [isFullScreenMode, isGlidingAway, isVisible, mouseReleased]);

  // Don't render anything if not visible (MUST be after all hooks)
  if (!isVisible) {
    return null;
  }

  return (
    <div className={`fixed inset-0 pointer-events-none ${className}`} style={{ zIndex: isFullScreenMode ? 30 : 10 }}>
      <div
        className="pointer-events-auto w-full h-full"
        onDoubleClick={(e) => {
          console.log('🖱️ Double-click detected in RobotAnimation');
          if (onScreenDoubleClick) {
            onScreenDoubleClick(e);
          }
        }}
        onContextMenu={(e) => {
          console.log('🖱️ Right-click (context menu) detected in RobotAnimation');
          if (onScreenContextMenu) {
            onScreenContextMenu(e);
          }
        }}
        onMouseUp={(e) => {
          console.log('🖱️ Mouse up detected in RobotAnimation, button:', e.button);
          if (onScreenMouseUp) {
            onScreenMouseUp(e);
          }
        }}
        onMouseDown={(e) => {
          console.log('🖱️ Mouse down detected in RobotAnimation, button:', e.button);
          if (onScreenMouseDown) {
            onScreenMouseDown(e);
          }
          if (onRightClickLeftClick && e.button === 0) { // Left mouse button
            onRightClickLeftClick(e);
          }
        }}
      >
        <Sketch
          preload={preload}
          setup={setup}
          draw={draw}
          mousePressed={safeMousePressed}
          mouseReleased={safeMouseReleased}
          windowResized={windowResized}
        />
      </div>
    </div>
  );
});

RobotAnimation.displayName = 'RobotAnimation';

export default RobotAnimation;
