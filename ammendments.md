Search Page Logic for AI Code Robot
This document outlines the desired behavior for the search functionality, including mode switching, refinement, and user interaction.

1. Initial Search and Mode Guessing
Starting Point: The user begins on the home page (as shown in image_3c8146.png), where they can input a search query into the "How can I help?" search bar.

AI's Role: Upon the first search from the home page, the AI must analyze the user's query and guess the most appropriate search mode:

Shopping Mode: If the query indicates an intent to purchase (e.g., "buy shoes," "Nike sneakers," "red dress").

Information Mode: If the query indicates an intent to gather information or learn (e.g., "history of shoes," "how to tie a knot," "what is AI?").

Automatic Mode Entry: The system will then automatically transition to the guessed mode (e.g., image_3c2f44.png for Shopping Mode, image_3c2f81.png for Information Mode) and display the relevant results.

2. Mode Persistence and Switching
Default Behavior: Once a mode (Shopping or Information) is entered, the system must remain in that mode for all subsequent searches and refinements.

User-Initiated Mode Change: The only ways to change the active mode are:

Manual Toggle: The user explicitly clicks on the "Shopping mode" or "Information mode" button/label (as seen in image_3c2f44.png and image_3c2f81.png).

No Automatic Switching on Subsequent Searches: After the initial mode guess, performing a new search or refining an existing search must not cause the AI to re-guess the mode or switch to the other mode, unless the user repeats the exact same search query. The system should process the search within the currently active mode.

Repeating Same Search: If the user repeats the exact same search query, the system should change to the other mode. For example, if currently in Shopping Mode, it switches to Information Mode, and vice-versa.

3. Refine Mode (ON/OFF Toggle)
Refine Button Visibility: The "REFINE" button and the associated "Refine your search here" input field (visible in image_3c2f44.png and image_3c2f81.png) should only be visible when "Refine Mode" is explicitly ON.

Standard Search Icon: When "Refine Mode" is OFF, the "REFINE" button should be replaced by a standard search icon (magnifying glass), and all searches will be standard, without any AI refinement logic based on previous searches.

Toggle Mechanism: A clear user interface element (e.g., a dedicated toggle switch, checkbox, or button) is required to allow the user to turn "Refine Mode" ON and OFF. This mechanism is not shown in the provided images, so it needs to be implemented.

4. "Buy" Button Relocation
Current State: The image implies an "immediate 'buy'" button associated with the search bar (though not explicitly visible in the provided snippets).

Required Change: The "immediate 'buy' button" (which uses "daswos coins") must be moved from any position near the search bar to the product listing itself. This means each individual product displayed in Shopping Mode should have its own "Buy" button.

5. Live Refinement Updates
Condition: This logic applies only when "Refine Mode" is ON.

Refinement Tags: When a user refines a search (e.g., searching for "shoes," then "red," then "Nike"), these refinement terms should appear as distinct, removable tags (e.g., "red," "shoes" tags in image_3c2f44.png).

Immediate Updates: If the user deletes one of these refinement tags (e.g., clicks the 'x' on the "red" tag), the AI search must:

Immediately: Recognize the removal of the specific refinement term.

Re-evaluate: Perform a new search query based on the remaining refinement terms.

Live Update: Display the updated search results instantaneously, reflecting the removal of that refinement.

Example:

User searches: "shoes" (Refine Mode ON)

User refines: "red" -> Results: "red shoes"

User refines: "Nike" -> Results: "Nike red shoes"

User removes "red" tag -> Immediate Update: Results should change to "Nike shoes".

Universality: This live update logic must apply consistently to all searches and refinement removals, regardless of the terms used.

By following these instructions, the AI code robot should be able to implement the desired search page functionality accurately.