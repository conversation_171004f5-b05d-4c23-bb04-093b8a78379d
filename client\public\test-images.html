<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image URL Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border: 2px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 4px;
            text-align: center;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .loading { background-color: #fff3cd; color: #856404; }
        h1 { text-align: center; color: #333; }
        h3 { margin-top: 0; color: #555; }
    </style>
</head>
<body>
    <h1>🖼️ Image URL Test - DasWos Cart & Checkout</h1>
    <p style="text-align: center; color: #666;">Testing image URLs used in cart and checkout pages</p>
    
    <div class="test-container">
        <!-- Placeholder SVG -->
        <div class="test-item">
            <h3>Placeholder SVG</h3>
            <img src="/placeholder-product.svg" alt="Placeholder" class="test-image" 
                 onload="this.nextElementSibling.textContent = '✅ Loaded successfully'; this.nextElementSibling.className = 'status success';"
                 onerror="this.nextElementSibling.textContent = '❌ Failed to load'; this.nextElementSibling.className = 'status error';">
            <p class="status loading">Loading...</p>
        </div>

        <!-- Sample Unsplash URLs from database -->
        <div class="test-item">
            <h3>Unsplash - Headphones</h3>
            <img src="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop" alt="Headphones" class="test-image"
                 onload="this.nextElementSibling.textContent = '✅ Loaded successfully'; this.nextElementSibling.className = 'status success';"
                 onerror="this.nextElementSibling.textContent = '❌ Failed to load'; this.nextElementSibling.className = 'status error';">
            <p class="status loading">Loading...</p>
        </div>

        <div class="test-item">
            <h3>Unsplash - Running Shoes</h3>
            <img src="https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop" alt="Running Shoes" class="test-image"
                 onload="this.nextElementSibling.textContent = '✅ Loaded successfully'; this.nextElementSibling.className = 'status success';"
                 onerror="this.nextElementSibling.textContent = '❌ Failed to load'; this.nextElementSibling.className = 'status error';">
            <p class="status loading">Loading...</p>
        </div>

        <div class="test-item">
            <h3>Unsplash - Wallet</h3>
            <img src="https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop" alt="Wallet" class="test-image"
                 onload="this.nextElementSibling.textContent = '✅ Loaded successfully'; this.nextElementSibling.className = 'status success';"
                 onerror="this.nextElementSibling.textContent = '❌ Failed to load'; this.nextElementSibling.className = 'status error';">
            <p class="status loading">Loading...</p>
        </div>

        <div class="test-item">
            <h3>Unsplash - Laptop</h3>
            <img src="https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=400&fit=crop" alt="Laptop" class="test-image"
                 onload="this.nextElementSibling.textContent = '✅ Loaded successfully'; this.nextElementSibling.className = 'status success';"
                 onerror="this.nextElementSibling.textContent = '❌ Failed to load'; this.nextElementSibling.className = 'status error';">
            <p class="status loading">Loading...</p>
        </div>

        <div class="test-item">
            <h3>Unsplash - Watch</h3>
            <img src="https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop" alt="Watch" class="test-image"
                 onload="this.nextElementSibling.textContent = '✅ Loaded successfully'; this.nextElementSibling.className = 'status success';"
                 onerror="this.nextElementSibling.textContent = '❌ Failed to load'; this.nextElementSibling.className = 'status error';">
            <p class="status loading">Loading...</p>
        </div>
    </div>

    <div style="margin-top: 40px; text-align: center; color: #666;">
        <p>If Unsplash images fail to load, they will fallback to the placeholder SVG in the actual application.</p>
        <p><a href="/checkout" style="color: #007bff;">← Back to Checkout</a></p>
    </div>
</body>
</html>
