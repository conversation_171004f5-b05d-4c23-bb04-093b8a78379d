# DasWos Coins Architecture

## Overview
DasWos Coins are the virtual currency used within the DasWos ecosystem. This document outlines the architecture where **user accounts hold the balance** and **wallets serve as the interface** for accessing and using those coins.

## Core Principles

### 1. **User-Centric Balance Storage**
- ✅ DasWos coins balance is stored in the **user account** (main daswos-18 database)
- ✅ Balance is tied to the **authenticated user**, not the wallet
- ✅ Users can access their balance from any connected wallet
- ✅ Full accountability and audit trail per user

### 2. **Wallet as Interface**
- ✅ Wallet is a **tool/interface** for accessing user's coins
- ✅ Wallet does **NOT store** the actual balance
- ✅ Wallet becomes **usable only when user is logged in**
- ✅ Multiple wallets can access the same user balance

### 3. **Dual Authentication Required**
- ✅ **User must be logged in** to daswos-18 (for balance access)
- ✅ **Wallet must be connected** (for transaction interface)
- ✅ Both conditions required for full functionality

## Architecture Flow

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User Login    │    │ Wallet Connect  │    │ DasWos Coins    │
│   (daswos-18)   │    │   (Interface)   │    │   Available     │
│                 │    │                 │    │                 │
│ ✓ Authenticated │ +  │ ✓ Connected     │ =  │ ✓ Balance Shows │
│ ✓ Has Balance   │    │ ✓ Usable        │    │ ✓ Can Transact  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Implementation Details

### Database Schema

#### Users Table (daswos-18 database)
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password TEXT NOT NULL,
    -- DasWos Coins balance stored with user account
    daswos_coins_balance INTEGER DEFAULT 0 NOT NULL,
    trust_score INTEGER DEFAULT 30 NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### DasWos Coins Transactions Table (daswos-18 database)
```sql
CREATE TABLE daswos_coins_transactions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    amount INTEGER NOT NULL, -- Positive for add, negative for spend
    transaction_type TEXT NOT NULL, -- 'purchase', 'spend', 'refund', 'bonus'
    description TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'completed',
    metadata JSONB DEFAULT '{}',
    wallet_id TEXT, -- Which wallet was used for this transaction
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Wallet Connections Table (wallet database)
```sql
CREATE TABLE wallet_connections (
    id SERIAL PRIMARY KEY,
    wallet_id TEXT NOT NULL,
    database_name TEXT NOT NULL, -- 'daswos-18'
    user_id INTEGER NOT NULL, -- User ID from daswos-18
    username TEXT NOT NULL,
    connected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### User Experience Flow

#### 1. **User Login Only**
```
User State: ✓ Logged In, ✗ No Wallet
Display: "Connect a wallet to access your X DasWos Coins"
Actions: Can view balance, cannot make transactions
```

#### 2. **Wallet Connected Only**
```
User State: ✗ Not Logged In, ✓ Wallet Connected  
Display: "Login to access your DasWos Coins"
Actions: Cannot view balance, cannot make transactions
```

#### 3. **Both User + Wallet**
```
User State: ✓ Logged In, ✓ Wallet Connected
Display: "X DasWos Coins" (from user account)
Actions: Can view balance, can make transactions
```

### API Endpoints

#### Get User Balance
```typescript
GET /api/user/daswos-coins/balance
// Returns user's balance from their account
// Requires: User authentication
// Returns: { balance: number, user_id: number }
```

#### Purchase Coins
```typescript
POST /api/user/daswos-coins/purchase
// Adds coins to user's account balance
// Requires: User authentication
// Body: { amount: number, payment_method?: string }
// Updates: users.daswos_coins_balance
// Creates: Transaction record with wallet_id if wallet connected
```

#### Spend Coins
```typescript
POST /api/user/daswos-coins/spend
// Deducts coins from user's account balance
// Requires: User authentication + Wallet connection
// Body: { amount: number, description: string, wallet_id: string }
// Updates: users.daswos_coins_balance
// Creates: Transaction record with wallet_id
```

### Frontend Implementation

#### Wallet Component Logic
```typescript
const WalletBalance = () => {
  const { user } = useAuth(); // User authentication state
  const { wallet } = useWallet(); // Wallet connection state
  
  // Only fetch balance if user is logged in
  const { data: balance } = useQuery({
    queryKey: ['/api/user/daswos-coins/balance'],
    queryFn: fetchUserBalance,
    enabled: !!user // Only when user is authenticated
  });
  
  if (!user) {
    return <div>Login to access your DasWos Coins</div>;
  }
  
  if (!wallet) {
    return <div>Connect a wallet to use your {balance} DasWos Coins</div>;
  }
  
  return (
    <div>
      <div>{balance} DasWos Coins</div>
      <button onClick={handlePurchase}>Add Coins</button>
      <button onClick={handleSpend}>Use Coins</button>
    </div>
  );
};
```

## Benefits of This Architecture

### 1. **User Ownership**
- Users own their coins regardless of which wallet they use
- Balance persists across different wallet connections
- Clear ownership and accountability

### 2. **Wallet Flexibility**
- Users can connect different wallets to access same balance
- Wallets are just interfaces, not storage systems
- Easy to add new wallet types

### 3. **Security & Audit**
- All transactions tied to authenticated users
- Complete audit trail of who spent what
- No anonymous transactions

### 4. **Scalability**
- User balances managed in main application database
- Wallet system can be scaled independently
- Clear separation of concerns

## Migration Strategy

1. **Restore user balance storage** in daswos-18 database
2. **Update API endpoints** to read/write user balances
3. **Modify wallet components** to display user balance when both authenticated
4. **Update transaction logic** to require both user auth + wallet connection
5. **Add wallet_id tracking** to all transactions for audit purposes

This architecture provides the best of both worlds: user-owned balances with wallet-based transaction interfaces!
