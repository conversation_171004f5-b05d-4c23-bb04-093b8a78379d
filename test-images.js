import postgres from 'postgres';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testImages() {
  if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL environment variable is required');
    process.exit(1);
  }

  try {
    // Test database connection
    const sql = postgres(process.env.DATABASE_URL, {
      ssl: 'require',
      max: 1,
      connect_timeout: 10,
      prepare: false,
    });

    console.log('🔍 Checking product images...');
    
    // Get wallet products with their image URLs
    const walletProducts = await sql`
      SELECT id, title, image_url, trust_score, status
      FROM products 
      WHERE title ILIKE '%wallet%' 
      AND status = 'active'
      AND trust_score >= 70
      ORDER BY id
    `;

    console.log(`📊 Found ${walletProducts.length} wallet products:`);
    
    walletProducts.forEach((product, index) => {
      console.log(`\n${index + 1}. Product ID: ${product.id}`);
      console.log(`   Title: ${product.title}`);
      console.log(`   Image URL: ${product.image_url}`);
      console.log(`   Trust Score: ${product.trust_score}`);
      console.log(`   Status: ${product.status}`);
    });

    // Test if the image URLs are accessible
    console.log('\n🌐 Testing image URL accessibility...');
    
    for (const product of walletProducts.slice(0, 3)) { // Test first 3
      try {
        const response = await fetch(product.image_url);
        console.log(`✅ ${product.title}: ${response.status} ${response.statusText}`);
        console.log(`   Content-Type: ${response.headers.get('content-type')}`);
      } catch (error) {
        console.log(`❌ ${product.title}: ${error.message}`);
      }
    }
    
    await sql.end();
    console.log('\n✅ Test completed successfully');
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

testImages();
