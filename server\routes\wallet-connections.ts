import express from 'express';
import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcrypt';
import { TransactionLogger } from '../transaction-logger';
import { isAuthenticated } from '../middleware/auth';
import { db } from '../db';
import { users, walletCards } from '@shared/schema1'; // Updated to use main database schema
import { eq } from 'drizzle-orm';

const router = express.Router();

// Wallet database configuration
const WALLET_DATABASE_CONFIG = {
  SUPABASE_URL: 'https://mjyaqqsxhkqyzqufpxzl.supabase.co',
  SUPABASE_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1qeWFxcXN4aGtxeXpxdWZweHpsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYyMzU4MiwiZXhwIjoyMDY0MTk5NTgyfQ.1LBqR-7c8UdBa8koW69nQrYSr_Lm1aErRUgoRteFFVs'
};

// Initialize Supabase client for wallet database
const walletSupabase = createClient(
  WALLET_DATABASE_CONFIG.SUPABASE_URL,
  WALLET_DATABASE_CONFIG.SUPABASE_KEY
);

/**
 * POST /api/wallet/create-connection
 * Create a connection record between a user and a wallet
 */
router.post('/create-connection', async (req, res) => {
  try {
    const {
      wallet_id,
      wallet_identifier,
      database_name,
      user_id,
      username,
      is_primary = false
    } = req.body;

    // Validate required fields
    if (!wallet_id || !database_name || !user_id || !username) {
      return res.status(400).json({
        error: 'Missing required fields: wallet_id, database_name, user_id, username'
      });
    }

    // Validate database name
    if (!['daswos-18', 'current-brobot-1'].includes(database_name)) {
      return res.status(400).json({
        error: 'Invalid database_name. Must be "daswos-18" or "current-brobot-1"'
      });
    }

    console.log('Creating wallet connection:', {
      wallet_id,
      wallet_identifier,
      database_name,
      user_id,
      username,
      is_primary
    });

    // Check if connection already exists
    const { data: existingConnection, error: checkError } = await walletSupabase
      .from('wallet_connections')
      .select('*')
      .eq('wallet_id', wallet_id)
      .eq('database_name', database_name)
      .eq('user_id', user_id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows found
      console.error('Error checking existing connection:', checkError);
      return res.status(500).json({
        error: 'Failed to check existing connection',
        details: checkError.message
      });
    }

    if (existingConnection) {
      // Connection already exists, update last_used timestamp
      const { data: updatedConnection, error: updateError } = await walletSupabase
        .from('wallet_connections')
        .update({
          last_used: new Date().toISOString(),
          is_primary: is_primary
        })
        .eq('id', existingConnection.id)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating existing connection:', updateError);
        return res.status(500).json({
          error: 'Failed to update existing connection',
          details: updateError.message
        });
      }

      return res.json({
        success: true,
        connection: updatedConnection,
        message: 'Existing connection updated'
      });
    }

    // Create new connection
    const { data: newConnection, error: insertError } = await walletSupabase
      .from('wallet_connections')
      .insert({
        wallet_id,
        database_name,
        user_id,
        username,
        is_primary,
        connected_at: new Date().toISOString(),
        last_used: new Date().toISOString()
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error creating wallet connection:', insertError);
      return res.status(500).json({
        error: 'Failed to create wallet connection',
        details: insertError.message
      });
    }

    console.log('Wallet connection created successfully:', newConnection);

    res.json({
      success: true,
      connection: newConnection,
      message: 'Wallet connection created successfully'
    });

  } catch (error) {
    console.error('Wallet connection creation error:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

/**
 * GET /api/wallet/connections/:user_id
 * Get all wallet connections for a user
 */
router.get('/connections/:user_id', async (req, res) => {
  try {
    const { user_id } = req.params;
    const { database_name = 'daswos-18' } = req.query;

    const { data: connections, error } = await walletSupabase
      .from('wallet_connections')
      .select(`
        *,
        wallets:wallet_id (
          wallet_id,
          balance,
          created_at,
          is_active
        )
      `)
      .eq('user_id', user_id)
      .eq('database_name', database_name)
      .order('connected_at', { ascending: false });

    if (error) {
      console.error('Error fetching wallet connections:', error);
      return res.status(500).json({
        error: 'Failed to fetch wallet connections',
        details: error.message
      });
    }

    res.json({
      success: true,
      connections
    });

  } catch (error) {
    console.error('Error fetching wallet connections:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

/**
 * DELETE /api/wallet/disconnect/:connection_id
 * Remove a wallet connection
 */
router.delete('/disconnect/:connection_id', async (req, res) => {
  try {
    const { connection_id } = req.params;

    const { error } = await walletSupabase
      .from('wallet_connections')
      .delete()
      .eq('id', connection_id);

    if (error) {
      console.error('Error disconnecting wallet:', error);
      return res.status(500).json({
        error: 'Failed to disconnect wallet',
        details: error.message
      });
    }

    res.json({
      success: true,
      message: 'Wallet disconnected successfully'
    });

  } catch (error) {
    console.error('Error disconnecting wallet:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

/**
 * POST /api/wallet/add-funds
 * Record a fund addition transaction with user tracking
 */
router.post('/add-funds', async (req, res) => {
  try {
    const {
      wallet_id,
      amount,
      user_id,
      username,
      database_name = 'daswos-18',
      description = 'Funds added via DasWos Coins purchase'
    } = req.body;

    // Validate required fields
    if (!wallet_id || !amount || !user_id || !username) {
      return res.status(400).json({
        error: 'Missing required fields: wallet_id, amount, user_id, username'
      });
    }

    // Get current wallet balance
    const { data: wallet, error: walletError } = await walletSupabase
      .from('wallets')
      .select('balance')
      .eq('id', wallet_id)
      .single();

    if (walletError) {
      console.error('Error fetching wallet:', walletError);
      return res.status(500).json({
        error: 'Failed to fetch wallet information',
        details: walletError.message
      });
    }

    const balanceBefore = parseFloat(wallet.balance);
    const balanceAfter = balanceBefore + parseFloat(amount);

    // Create transaction record
    const { data: transaction, error: transactionError } = await walletSupabase
      .from('transactions')
      .insert({
        wallet_id,
        transaction_type: 'add_funds',
        amount: parseFloat(amount),
        balance_before: balanceBefore,
        balance_after: balanceAfter,
        description,
        reference_type: 'daswos_coins_purchase',
        reference_id: `user_${user_id}_${Date.now()}`,
        status: 'completed'
      })
      .select()
      .single();

    if (transactionError) {
      console.error('Error creating transaction:', transactionError);
      return res.status(500).json({
        error: 'Failed to record transaction',
        details: transactionError.message
      });
    }

    // Update wallet balance
    const { error: updateError } = await walletSupabase
      .from('wallets')
      .update({
        balance: balanceAfter,
        updated_at: new Date().toISOString()
      })
      .eq('id', wallet_id);

    if (updateError) {
      console.error('Error updating wallet balance:', updateError);
      return res.status(500).json({
        error: 'Failed to update wallet balance',
        details: updateError.message
      });
    }

    // Update connection last_used timestamp
    await walletSupabase
      .from('wallet_connections')
      .update({ last_used: new Date().toISOString() })
      .eq('wallet_id', wallet_id)
      .eq('user_id', user_id)
      .eq('database_name', database_name);

    console.log(`Funds added: ${amount} DasWos Coins to wallet ${wallet_id} by user ${username} (${user_id})`);

    res.json({
      success: true,
      transaction,
      new_balance: balanceAfter,
      message: `Successfully added ${amount} DasWos Coins to wallet`
    });

  } catch (error) {
    console.error('Error adding funds:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

/**
 * GET /api/wallet/fund-history/:wallet_id
 * Get fund addition history for a wallet with user information
 */
router.get('/fund-history/:wallet_id', async (req, res) => {
  try {
    const { wallet_id } = req.params;

    // Get transactions with connection information
    const { data: transactions, error } = await walletSupabase
      .from('transactions')
      .select(`
        *,
        wallet_connections!inner (
          user_id,
          username,
          database_name
        )
      `)
      .eq('wallet_id', wallet_id)
      .eq('transaction_type', 'add_funds')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching fund history:', error);
      return res.status(500).json({
        error: 'Failed to fetch fund history',
        details: error.message
      });
    }

    res.json({
      success: true,
      transactions
    });

  } catch (error) {
    console.error('Error fetching fund history:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

/**
 * POST /api/wallet/login
 * Login to existing wallet
 */
router.post('/login', async (req, res) => {
  try {
    const { walletId, password, userId, username } = req.body;

    console.log(`🔐 Wallet login attempt: ${walletId} for user ${username} (ID: ${userId})`);

    // Validate inputs
    if (!walletId || !password) {
      return res.status(400).json({ error: 'Wallet ID and password are required' });
    }

    // Hash password using same method as create
    const crypto = await import('crypto');
    const passwordHash = crypto.createHash('sha256').update(password + 'wallet_salt').digest('hex');

    // Check if wallet exists and verify password
    const { data: wallet, error: walletError } = await walletSupabase
      .from('wallets')
      .select('*')
      .eq('wallet_id', walletId)
      .eq('password_hash', passwordHash)
      .eq('is_active', true)
      .single();

    if (walletError || !wallet) {
      console.log(`❌ Wallet login failed: ${walletId} - Invalid credentials`);
      return res.status(401).json({ error: 'Invalid wallet ID or password' });
    }

    console.log(`✅ Wallet login successful: ${walletId} for user ${username}`);

    // Log wallet connection
    if (userId && username) {
      TransactionLogger.walletConnect(parseInt(userId), username, walletId);
    }

    // Update last accessed time
    await walletSupabase
      .from('wallets')
      .update({ last_accessed: new Date().toISOString() })
      .eq('wallet_id', walletId);

    // Create wallet session
    const sessionToken = 'wallet_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days

    const { data: sessionData, error: sessionError } = await walletSupabase
      .from('wallet_sessions')
      .insert({
        wallet_id: wallet.id, // Use the UUID from the wallet record, not the wallet_id string
        session_token: sessionToken,
        expires_at: expiresAt.toISOString(),
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (sessionError) {
      console.error('Failed to create wallet session:', sessionError);
      // Don't fail the request - wallet login was successful
    }

    // Create wallet connection record if user info provided
    if (userId && username) {
      const { data: existingConnection } = await walletSupabase
        .from('wallet_connections')
        .select('*')
        .eq('wallet_id', wallet.id) // Use UUID, not wallet_id string
        .eq('user_id', userId)
        .eq('database_name', 'daswos-18')
        .single();

      if (!existingConnection) {
        await walletSupabase
          .from('wallet_connections')
          .insert({
            wallet_id: wallet.id, // Use UUID, not wallet_id string
            user_id: userId,
            username: username,
            database_name: 'daswos-18',
            connected_at: new Date().toISOString(),
            is_active: true,
            is_primary: true
          });
      } else {
        // Update last used time
        await walletSupabase
          .from('wallet_connections')
          .update({ last_used: new Date().toISOString() })
          .eq('id', existingConnection.id);
      }
    }

    res.json({
      success: true,
      wallet: {
        id: wallet.id,
        wallet_id: wallet.wallet_id,
        created_at: wallet.created_at,
        last_accessed: wallet.last_accessed
      },
      session: {
        token: sessionToken,
        expires_at: expiresAt.toISOString()
      }
    });

  } catch (error) {
    console.error('Error logging into wallet:', error);
    res.status(500).json({ error: 'Failed to login to wallet' });
  }
});

/**
 * POST /api/wallet/create
 * Create/Activate wallet with password
 * REQUIRES AUTHENTICATION: Only logged-in users can create wallets
 */
router.post('/create', async (req, res) => {
  try {
    const { walletId, password } = req.body;

    // CRITICAL: Check if user is authenticated via session first
    let userId, username;

    if (typeof req.isAuthenticated === 'function' && req.isAuthenticated() && req.user) {
      // User is authenticated via session - use session data
      userId = req.user.id;
      username = req.user.username;
      console.log(`🔐 Wallet creation: User authenticated via session - ${username} (ID: ${userId})`);
    } else {
      // Fallback to request body (for backwards compatibility)
      userId = req.body.userId || req.headers['x-user-id'];
      username = req.body.username || req.headers['x-username'];
      console.log(`🔐 Wallet creation: Using fallback auth - ${username} (ID: ${userId})`);
    }

    // Ensure we have user information
    if (!userId || !username) {
      console.log(`❌ Wallet creation failed: Missing user authentication`);
      return res.status(401).json({
        error: 'Authentication required. Please log in to create a wallet.'
      });
    }

    // Validate inputs
    if (!walletId || !password) {
      return res.status(400).json({ error: 'Wallet ID and password are required' });
    }

    if (password.length < 6) {
      return res.status(400).json({ error: 'Password must be at least 6 characters' });
    }

    // SECURITY: Check if wallet ID already exists with a password (prevent reuse)
    const { data: existingWalletCheck, error: checkError } = await walletSupabase
      .from('wallets')
      .select('wallet_id, password_hash')
      .eq('wallet_id', walletId)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows found
      console.error('Error checking existing wallet:', checkError);
      return res.status(500).json({ error: 'Failed to verify wallet availability' });
    }

    // If wallet exists and already has a REAL password (not temporary), reject the request
    if (existingWalletCheck &&
        existingWalletCheck.password_hash &&
        existingWalletCheck.password_hash !== 'TEMP_PENDING_ACTIVATION') {
      return res.status(409).json({
        error: 'Wallet ID already in use',
        message: 'This wallet ID has already been activated. Please use a different wallet ID.',
        code: 'WALLET_ID_TAKEN'
      });
    }

    // Hash password using simple method (in production, use proper scrypt)
    const crypto = await import('crypto');
    const passwordHash = crypto.createHash('sha256').update(password + 'wallet_salt').digest('hex');

    // Check if wallet exists and update password, or create new wallet
    const { data: existingWallet, error: walletCheckError } = await walletSupabase
      .from('wallets')
      .select('*')
      .eq('wallet_id', walletId)
      .single();

    console.log(`🔍 Wallet check for ${walletId}:`, {
      exists: !!existingWallet,
      hasPassword: !!existingWallet?.password_hash,
      passwordValue: existingWallet?.password_hash,
      isTemp: existingWallet?.password_hash === 'TEMP_PENDING_ACTIVATION'
    });

    let walletData;
    if (existingWallet) {
      // Check if this wallet has no password yet (password_hash is null, undefined, or temporary)
      const needsPassword = !existingWallet.password_hash ||
                           existingWallet.password_hash === 'TEMP_PENDING_ACTIVATION';

      // SECURITY: Prevent password creation for wallets that already have REAL passwords
      // Users can only update passwords through the password reset endpoint
      if (!needsPassword) {
        return res.status(400).json({
          error: 'Wallet already activated',
          message: 'This wallet already has a password. Use the password reset feature to change it.',
          code: 'WALLET_ALREADY_ACTIVATED'
        });
      }

      // Update existing wallet with password (first-time activation only)
      const { data: updatedWallet, error: updateError } = await walletSupabase
        .from('wallets')
        .update({
          password_hash: passwordHash,
          is_active: true, // Ensure wallet is active
          last_accessed: new Date().toISOString()
        })
        .eq('wallet_id', walletId)
        .select()
        .single();

      if (updateError) {
        console.error('Failed to update wallet password:', updateError);
        return res.status(500).json({ error: 'Failed to activate wallet' });
      }

      walletData = updatedWallet;
      console.log(`✅ Wallet password set for first time for wallet: ${walletId}`);
    } else {
      // Create new wallet with password
      const { data: newWallet, error: createError } = await walletSupabase
        .from('wallets')
        .insert({
          wallet_id: walletId,
          password_hash: passwordHash,
          is_active: true,
          created_at: new Date().toISOString(),
          last_accessed: new Date().toISOString()
        })
        .select()
        .single();

      if (createError) {
        console.error('Failed to create wallet:', createError);
        return res.status(500).json({ error: 'Failed to create wallet' });
      }

      walletData = newWallet;
      console.log(`✅ New wallet created: ${walletId}`);
    }

    // Create primary card for the wallet
    console.log(`🔄 Attempting to create primary card for wallet: ${walletId}, userId: ${userId}`);
    if (userId) {
      try {
        const cardId = `card-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        console.log(`💳 Creating primary card with ID: ${cardId}`);

        const [newCard] = await db.insert(walletCards).values({
          userId: parseInt(userId),
          walletId: walletId,
          cardId: cardId,
          cardName: 'Primary Card',
          balance: 0,
          totalEarned: 0,
          totalSpent: 0,
          isPrimary: true,
          isActive: true,
          createdAt: new Date()
        }).returning();

        console.log(`✅ Primary card created for wallet: ${walletId}, cardId: ${cardId}`, newCard);

        // Set this card as the user's active card
        try {
          await db
            .update(users)
            .set({ activeCardId: cardId })
            .where(eq(users.id, parseInt(userId)));
          console.log(`✅ Set primary card ${cardId} as active for user ${userId}`);
        } catch (activeCardError) {
          console.error('❌ Failed to set primary card as active:', activeCardError);
        }
      } catch (error) {
        console.error('❌ Error creating primary card:', error);
        // Don't fail the request - wallet was created successfully
      }
    } else {
      console.log(`❌ No userId provided for primary card creation`);
    }

    // Create wallet session
    const sessionToken = 'wallet_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days

    const { data: sessionData, error: sessionError } = await walletSupabase
      .from('wallet_sessions')
      .insert({
        wallet_id: walletData.id, // Use the UUID from the wallet record, not the wallet_id string
        session_token: sessionToken,
        expires_at: expiresAt.toISOString(),
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (sessionError) {
      console.error('Failed to create wallet session:', sessionError);
      // Don't fail the request - wallet was created successfully
    }

    // Create wallet connection if it doesn't exist and we have user info
    if (userId && username) {
      const { data: existingConnection } = await walletSupabase
        .from('wallet_connections')
        .select('*')
        .eq('wallet_id', walletData.id) // Use UUID, not wallet_id string
        .eq('user_id', userId)
        .eq('database_name', 'daswos-18')
        .single();

      if (!existingConnection) {
        await walletSupabase
          .from('wallet_connections')
          .insert({
            wallet_id: walletData.id, // Use UUID, not wallet_id string
            user_id: userId,
            username: username,
            database_name: 'daswos-18',
            connected_at: new Date().toISOString(),
            is_active: true,
            is_primary: true
          });
      }

      // Update user's wallet list in main database
      try {
        console.log(`🔄 Updating wallet list for user ${userId} (${username})`);
        const userRecord = await db.select().from(users).where(eq(users.id, parseInt(userId))).limit(1);
        console.log(`📋 Current user record:`, userRecord[0]);

        if (userRecord.length > 0) {
          const currentWalletIds = userRecord[0].walletIds || [];
          console.log(`📋 Current wallet IDs:`, currentWalletIds);

          // Add the new wallet ID if it's not already in the list
          if (!currentWalletIds.includes(walletId)) {
            const updatedWalletIds = [...currentWalletIds, walletId];
            console.log(`📋 Updated wallet IDs:`, updatedWalletIds);

            await db.update(users)
              .set({ walletIds: updatedWalletIds })
              .where(eq(users.id, parseInt(userId)));

            console.log(`✅ Added wallet ${walletId} to user ${username}'s wallet list`);
          } else {
            console.log(`ℹ️ Wallet ${walletId} already in user's list`);
          }

          // SECURITY: Generate a new assigned wallet ID for future wallet creation
          // This prevents the same wallet ID from being reused
          const currentUser = userRecord[0];

          // SECURITY: primaryWalletId should NEVER change - it's the first wallet created
          // Set the activated wallet as the active wallet
          await db.update(users)
            .set({
              activeWalletId: walletId, // Set the activated wallet as active
              // NOTE: primaryWalletId remains unchanged - it's always the first wallet
            })
            .where(eq(users.id, parseInt(userId)));

          console.log(`📝 Activated wallet ${walletId} is now available for login`);
          console.log(`🔒 Primary wallet ID remains unchanged: ${currentUser.primaryWalletId}`);
          console.log(`✅ Active wallet ID set to: ${walletId}`);
        } else {
          console.log(`❌ User record not found for ID ${userId}`);
        }
      } catch (error) {
        console.error('❌ Error updating user wallet list:', error);
        // Don't fail the request - wallet was created successfully
      }
    }

    res.json({
      success: true,
      wallet: {
        id: walletData.id,
        wallet_id: walletData.wallet_id,
        created_at: walletData.created_at,
        last_accessed: walletData.last_accessed
      },
      session: {
        token: sessionToken,
        expires_at: expiresAt.toISOString()
      }
    });

  } catch (error) {
    console.error('Error creating/activating wallet:', error);
    res.status(500).json({ error: 'Failed to create wallet' });
  }
});

/**
 * POST /api/wallet/reset-password
 * Reset wallet password with identity verification
 * REQUIRES AUTHENTICATION: Only logged-in users can reset wallet passwords
 */
router.post('/reset-password', async (req, res) => {
  try {
    const { walletId, newPassword, identityVerification, userId, username } = req.body;

    // Validate inputs
    if (!walletId || !newPassword || !identityVerification) {
      return res.status(400).json({
        error: 'Wallet ID, new password, and identity verification are required'
      });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({
        error: 'Password must be at least 6 characters'
      });
    }

    // Verify that the user owns this wallet
    const { data: connection, error: connectionError } = await walletSupabase
      .from('wallet_connections')
      .select('*')
      .eq('wallet_id', walletId)
      .eq('user_id', userId)
      .eq('database_name', 'daswos-18')
      .single();

    if (connectionError || !connection) {
      return res.status(403).json({
        error: 'Wallet not found or access denied',
        message: 'You can only reset passwords for wallets linked to your account'
      });
    }

    // Additional identity verification (could be enhanced with more checks)
    if (identityVerification.username !== username) {
      return res.status(403).json({
        error: 'Identity verification failed',
        message: 'Username verification does not match'
      });
    }

    // Hash the new password
    const crypto = await import('crypto');
    const passwordHash = crypto.createHash('sha256').update(newPassword + 'wallet_salt').digest('hex');

    // Update wallet password
    const { data: updatedWallet, error: updateError } = await walletSupabase
      .from('wallets')
      .update({
        password_hash: passwordHash,
        updated_at: new Date().toISOString()
      })
      .eq('wallet_id', walletId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating wallet password:', updateError);
      return res.status(500).json({
        error: 'Failed to update wallet password',
        details: updateError.message
      });
    }

    // Log the password reset
    TransactionLogger.walletPasswordReset(userId, username, walletId);

    console.log(`✅ Wallet password reset successful: ${walletId} for user ${username} (${userId})`);

    res.json({
      success: true,
      message: 'Wallet password reset successfully',
      wallet: {
        id: updatedWallet.id,
        wallet_id: updatedWallet.wallet_id,
        updated_at: updatedWallet.updated_at
      }
    });

  } catch (error) {
    console.error('Error resetting wallet password:', error);
    res.status(500).json({
      error: 'Failed to reset wallet password',
      details: error.message
    });
  }
});

/**
 * POST /api/wallet/update-password
 * Update wallet password for authenticated user (no current password required)
 */
router.post('/update-password', isAuthenticated, async (req, res) => {
  try {
    const { walletId, newPassword, userId, username } = req.body;

    // Validate required fields
    if (!walletId || !newPassword || !userId || !username) {
      return res.status(400).json({
        error: 'Missing required fields: walletId, newPassword, userId, username'
      });
    }

    // Verify user owns this wallet
    const user = await db.select().from(users).where(eq(users.id, userId)).limit(1);
    if (!user.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userData = user[0];
    const userWalletIds = userData.walletIds || [];

    if (!userWalletIds.includes(walletId)) {
      return res.status(403).json({
        error: 'Access denied: Wallet not owned by user'
      });
    }

    // Verify user is authenticated to the correct account
    if ((req.user as any)?.id !== userId || (req.user as any)?.username !== username) {
      return res.status(403).json({
        error: 'Access denied: User authentication mismatch'
      });
    }

    // Hash the new password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(newPassword, saltRounds);

    // Update wallet password in wallet database
    const { data: updatedWallet, error: updateError } = await walletSupabase
      .from('wallets')
      .update({
        password_hash: passwordHash,
        last_accessed: new Date().toISOString()
      })
      .eq('wallet_id', walletId)
      .select()
      .single();

    if (updateError) {
      console.error('Failed to update wallet password:', updateError);
      return res.status(500).json({
        error: 'Failed to update wallet password',
        details: updateError.message
      });
    }

    if (!updatedWallet) {
      return res.status(404).json({ error: 'Wallet not found' });
    }

    console.log(`✅ Password updated for wallet: ${walletId} by user: ${username}`);

    res.json({
      success: true,
      message: 'Wallet password updated successfully',
      walletId: walletId
    });

  } catch (error) {
    console.error('Error in POST /api/wallet/update-password:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/wallet/check-activation/:wallet_id
 * Check if a specific wallet ID has been activated (has a real password)
 * SECURITY: This endpoint is crucial for preventing password overwrites
 */
router.get('/check-activation/:wallet_id', async (req, res) => {
  try {
    const { wallet_id } = req.params;

    if (!wallet_id) {
      return res.status(400).json({ error: 'Wallet ID is required' });
    }

    // Check if wallet exists and is activated in the wallet database
    const { data: wallet, error } = await walletSupabase
      .from('wallets')
      .select('wallet_id, password_hash, is_active, created_at')
      .eq('wallet_id', wallet_id)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = not found
      console.error('Error checking wallet activation:', error);
      return res.status(500).json({
        error: 'Failed to check wallet activation',
        details: error.message
      });
    }

    // Determine if wallet is activated
    const isActivated = wallet &&
                       wallet.password_hash &&
                       wallet.password_hash !== 'TEMP_PENDING_ACTIVATION' &&
                       wallet.is_active;

    res.json({
      success: true,
      walletId: wallet_id,
      isActivated: !!isActivated,
      exists: !!wallet,
      createdAt: wallet?.created_at || null
    });

  } catch (error) {
    console.error('Error in GET /api/wallet/check-activation:', error);
    res.status(500).json({
      error: 'Failed to check wallet activation',
      details: error.message
    });
  }
});

/**
 * GET /api/wallet/assigned-id/:user_id
 * Get the user's current assigned wallet ID for new wallet creation
 */
router.get('/assigned-id/:user_id', async (req, res) => {
  try {
    const { user_id } = req.params;

    // Get user's current assigned wallet ID from main database
    const userRecord = await db.select().from(users).where(eq(users.id, parseInt(user_id))).limit(1);

    if (userRecord.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    const user = userRecord[0];
    const assignedWalletId = user.walletId;

    // Check if this assigned wallet has been activated
    let isActivated = false;
    if (assignedWalletId) {
      const { data: walletCheck } = await walletSupabase
        .from('wallets')
        .select('wallet_id, password_hash, is_active')
        .eq('wallet_id', assignedWalletId)
        .single();

      isActivated = walletCheck &&
                   walletCheck.password_hash &&
                   walletCheck.password_hash !== 'TEMP_PENDING_ACTIVATION' &&
                   walletCheck.is_active;
    }

    res.json({
      success: true,
      assignedWalletId: assignedWalletId,
      isActivated: isActivated,
      walletId: user.walletId // Single wallet system
    });

  } catch (error) {
    console.error('Error getting assigned wallet ID:', error);
    res.status(500).json({
      error: 'Failed to get assigned wallet ID',
      details: error.message
    });
  }
});

export default router;
