import { useState, useEffect, useRef, useCallback } from 'react';
import { useLocation } from 'wouter';
import { useToast } from '@/hooks/use-toast';

interface VoiceRecognitionOptions {
  continuous?: boolean;
  interimResults?: boolean;
  language?: string;
}

interface VoiceCommand {
  command: string;
  confidence: number;
  timestamp: number;
}

export const useVoiceRecognition = (options: VoiceRecognitionOptions = {}) => {
  const [isListening, setIsListening] = useState(false);
  const [isSupported, setIsSupported] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [interimTranscript, setInterimTranscript] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [lastCommand, setLastCommand] = useState<VoiceCommand | null>(null);

  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const [, setLocation] = useLocation();
  const { toast } = useToast();

  // Initialize speech recognition
  useEffect(() => {
    console.log('Initializing speech recognition...');
    if (typeof window !== 'undefined') {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      console.log('SpeechRecognition available:', !!SpeechRecognition);

      if (SpeechRecognition) {
        setIsSupported(true);
        const recognition = new SpeechRecognition();

        recognition.continuous = options.continuous ?? true;
        recognition.interimResults = options.interimResults ?? true;
        recognition.lang = options.language ?? 'en-US';
        recognition.maxAlternatives = 1;

        recognitionRef.current = recognition;
        console.log('Speech recognition initialized successfully');
      } else {
        setIsSupported(false);
        setError('Speech recognition not supported in this browser');
        console.log('Speech recognition not supported');
      }
    } else {
      console.log('Window not available (SSR)');
    }
  }, [options.continuous, options.interimResults, options.language]);

  // Navigation commands processor
  const processNavigationCommand = useCallback((command: string) => {
    const lowerCommand = command.toLowerCase();

    // Navigation patterns
    const navigationPatterns = [
      // PRIORITY: Speak to Daswos command (highest priority)
      {
        pattern: /^(?:speak to|talk to|open|activate)\s+daswos$/i,
        action: 'activate_robot',
        description: 'Activating Daswos robot interface...'
      },

      // PRIORITY: Simple direct commands first (these should execute immediately)
      { pattern: /^(?:daswos\s+)?(?:sign in|log in|login|authenticate)$/i, route: '/auth' },
      { pattern: /^(?:daswos\s+)?(?:go\s+)?home$/i, route: '/' },
      { pattern: /^(?:daswos\s+)?coins$/i, route: '/daswos-coins' },
      { pattern: /^(?:daswos\s+)?cart$/i, route: '/cart' },
      { pattern: /^(?:daswos\s+)?autoshop$/i, route: '/autoshop-dashboard' },
      { pattern: /^(?:daswos\s+)?profile$/i, route: '/profile' },
      { pattern: /^(?:daswos\s+)?orders$/i, route: '/orders' },
      { pattern: /^(?:daswos\s+)?settings$/i, route: '/user-settings' },

      // Main navigation patterns (following DasWos Coins successful pattern)
      { pattern: /(?:daswos\s+)?(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?(?:daswos\s+)?coins/i, route: '/daswos-coins' },
      { pattern: /(?:daswos\s+)?(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?(?:shopping\s+)?cart/i, route: '/cart' },
      { pattern: /(?:daswos\s+)?(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?(?:auto\s*shop|autoshop)/i, route: '/autoshop-dashboard' },
      { pattern: /(?:daswos\s+)?(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?profile/i, route: '/profile' },
      { pattern: /(?:daswos\s+)?(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?orders/i, route: '/orders' },
      { pattern: /(?:daswos\s+)?(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?purchases/i, route: '/purchases' },
      { pattern: /(?:daswos\s+)?(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?listings/i, route: '/my-listings' },
      { pattern: /(?:daswos\s+)?(?:go to|navigate to|open|show me|take me to)\s+(?:user\s+)?settings/i, route: '/user-settings' },
      { pattern: /(?:daswos\s+)?(?:go to|navigate to|open|show me|take me to)\s+(?:the\s+)?(?:sign in|login|auth|authentication)(?:\s+page)?/i, route: '/auth' },
      { pattern: /(?:daswos\s+)?(?:go to|navigate to|open|show me|take me to)\s+(?:the\s+)?home(?:\s+page)?/i, route: '/' },
      { pattern: /(?:daswos\s+)?(?:go to|navigate to|open|show me|take me to)\s+(?:the\s+)?search(?:\s+page)?/i, route: '/search' },
      { pattern: /(?:daswos\s+)?(?:go to|navigate to|open|show me|take me to)\s+(?:the\s+)?sell(?:\s+page)?/i, route: '/sell' },
      { pattern: /(?:daswos\s+)?(?:go to|navigate to|open|show me|take me to)\s+(?:split\s+)?buy/i, route: '/split-buy' },
      { pattern: /(?:daswos\s+)?(?:go to|navigate to|open|show me|take me to)\s+(?:das\s+)?list/i, route: '/d-list' },
      { pattern: /(?:daswos\s+)?(?:go to|navigate to|open|show me|take me to)\s+(?:browse\s+)?jobs/i, route: '/browse-jobs' },
      { pattern: /(?:daswos\s+)?(?:go to|navigate to|open|show me|take me to)\s+(?:ai\s+)?assistant/i, route: '/ai-assistant' },

      // Additional common navigation patterns
      { pattern: /(?:daswos\s+)?(?:show|display)\s+(?:my\s+)?profile/i, route: '/profile' },
      { pattern: /(?:daswos\s+)?(?:show|display)\s+(?:my\s+)?cart/i, route: '/cart' },
      { pattern: /(?:daswos\s+)?(?:show|display)\s+(?:my\s+)?orders/i, route: '/orders' },
    ];

    for (const item of navigationPatterns) {
      if (item.pattern.test(lowerCommand)) {
        // Handle special "activate_robot" action
        if (item.action === 'activate_robot') {
          console.log(`🎯 Voice command: "${lowerCommand}" -> Activating Daswos robot`);
          // Dispatch event to activate robot overlay
          const activateEvent = new CustomEvent('activateRobot');
          window.dispatchEvent(activateEvent);

          toast({
            title: 'Daswos Activated',
            description: item.description || 'Opening Daswos robot interface...',
          });
          return true;
        }

        // Handle normal navigation
        if (item.route) {
          console.log(`🎯 Voice navigation: "${lowerCommand}" -> ${item.route}`);
          setLocation(item.route);
          toast({
            title: 'Navigation Command Executed',
            description: `Taking you to ${item.route}`,
          });
          return true;
        }
      }
    }

    return false;
  }, [setLocation, toast]);

  // Search command processor
  const processSearchCommand = useCallback((command: string): string | null => {
    const lowerCommand = command.toLowerCase();

    // Search patterns
    const searchPatterns = [
      /(?:daswos\s+)?(?:search for|find|look for|show me|find me)\s+(.+)/i,
      /(?:daswos|das wos)?\s*search\s+(.+)/i,
      /(?:i want to buy|i need|get me|buy me|purchase)\s+(.+)/i,
      /(?:daswos\s+)?(?:can you find|help me find|locate)\s+(.+)/i,
      /(?:daswos\s+)?(?:what about|how about|show me some)\s+(.+)/i,
      /(?:daswos\s+)?(?:i'm looking for|looking for)\s+(.+)/i,
    ];

    for (const pattern of searchPatterns) {
      const match = lowerCommand.match(pattern);
      if (match && match[1]) {
        let searchQuery = match[1].trim();

        // Remove common stop words and punctuation at the end
        searchQuery = searchQuery.replace(/\s+(please|now|today)$/i, '');
        searchQuery = searchQuery.replace(/[.!?]+$/g, ''); // Remove trailing punctuation
        searchQuery = searchQuery.trim();

        console.log(`🔍 Voice recognition cleaned search query: "${searchQuery}"`);
        return searchQuery;
      }
    }

    return null;
  }, []);

  // AutoShop command processor
  const processAutoShopCommand = useCallback((command: string): boolean => {
    const lowerCommand = command.toLowerCase();

    const autoShopPatterns = [
      /(?:daswos\s+)?(?:start|enable|activate|begin|turn on)\s+(?:auto\s*shop|autoshop)/i,
      /(?:daswos\s+)?(?:let's|lets)\s+(?:auto\s*shop|autoshop)/i,
      /(?:daswos\s+)?(?:auto\s*shop|autoshop)\s+(?:start|begin|go|now)/i,
      /(?:daswos\s+)?(?:start|begin)\s+(?:automatic\s+)?shopping/i,
      /(?:daswos\s+)?(?:shop for me|shop automatically)/i,
      /(?:daswos\s+)?(?:enable|activate)\s+(?:automatic\s+)?shopping/i,
    ];

    return autoShopPatterns.some(pattern => pattern.test(lowerCommand));
  }, []);

  // Purchase command processor
  const processPurchaseCommand = useCallback((command: string): { quantity: number } | null => {
    const lowerCommand = command.toLowerCase().trim();

    // "buy it daswos" command
    if (lowerCommand === 'buy it daswos') {
      return { quantity: 1 };
    }

    return null;
  }, []);

  // Main command processor
  const processCommand = useCallback((command: string, confidence: number) => {
    const commandData: VoiceCommand = {
      command,
      confidence,
      timestamp: Date.now()
    };

    setLastCommand(commandData);

    // Only process commands with reasonable confidence
    if (confidence < 0.7) {
      toast({
        title: 'Voice Command Unclear',
        description: 'Please speak more clearly or try again.',
        variant: 'destructive',
      });
      return null;
    }

    // Check for navigation commands first
    if (processNavigationCommand(command)) {
      return { type: 'navigation', command };
    }

    // Check for purchase commands
    const purchaseResult = processPurchaseCommand(command);
    if (purchaseResult) {
      return { type: 'purchase', quantity: purchaseResult.quantity, command };
    }

    // Check for AutoShop commands
    if (processAutoShopCommand(command)) {
      return { type: 'autoshop', command };
    }

    // Check for search commands
    const searchQuery = processSearchCommand(command);
    if (searchQuery) {
      return { type: 'search', query: searchQuery, command };
    }

    // If no specific pattern matched, treat as general search
    return { type: 'search', query: command, command };
  }, [processNavigationCommand, processSearchCommand, processAutoShopCommand, processPurchaseCommand, toast]);

  // Start listening
  const startListening = useCallback(() => {
    // Stop any ongoing speech when starting to listen
    if ('speechSynthesis' in window) {
      speechSynthesis.cancel();
      console.log('🔇 Stopped ongoing speech synthesis');
    }

    if (!recognitionRef.current || !isSupported) {
      setError('Speech recognition not available');
      return;
    }

    try {
      setError(null);
      setTranscript('');
      setInterimTranscript('');
      setIsListening(true);

      recognitionRef.current.start();

      toast({
        title: 'Voice Recognition Active',
        description: 'Listening for your command...',
      });
    } catch (err) {
      setError('Failed to start voice recognition');
      setIsListening(false);
    }
  }, [isSupported, toast]);

  // Stop listening
  const stopListening = useCallback(() => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
      setIsListening(false);
    }
  }, []);

  // Set up event listeners
  useEffect(() => {
    const recognition = recognitionRef.current;
    if (!recognition) return;

    const handleResult = (event: SpeechRecognitionEvent) => {
      let finalTranscript = '';
      let interimTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i];
        const transcript = result[0].transcript;

        if (result.isFinal) {
          finalTranscript += transcript;
        } else {
          interimTranscript += transcript;
        }
      }

      setTranscript(finalTranscript);
      setInterimTranscript(interimTranscript);

      // Process final results
      if (finalTranscript) {
        const confidence = event.results[event.results.length - 1][0].confidence;
        const result = processCommand(finalTranscript, confidence);

        if (result) {
          // Command processed successfully
          setIsListening(false);
        }
      }
    };

    const handleError = (event: SpeechRecognitionErrorEvent) => {
      setError(`Speech recognition error: ${event.error}`);
      setIsListening(false);

      if (event.error !== 'aborted') {
        toast({
          title: 'Voice Recognition Error',
          description: `Error: ${event.error}`,
          variant: 'destructive',
        });
      }
    };

    const handleEnd = () => {
      setIsListening(false);
    };

    recognition.addEventListener('result', handleResult);
    recognition.addEventListener('error', handleError);
    recognition.addEventListener('end', handleEnd);

    return () => {
      recognition.removeEventListener('result', handleResult);
      recognition.removeEventListener('error', handleError);
      recognition.removeEventListener('end', handleEnd);
    };
  }, [processCommand, toast]);

  return {
    isListening,
    isSupported,
    transcript,
    interimTranscript,
    error,
    lastCommand,
    startListening,
    stopListening,
    processCommand
  };
};
