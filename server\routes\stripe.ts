import { Router } from 'express';
import { z } from 'zod';
import { IStorage } from '../storage';
import {
  createCustomer,
  createSubscription,
  getPriceId
} from '../stripe';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-02-24.acacia',
});

export function createStripeRoutes(storage: IStorage) {
  const router = Router();

  // Test Stripe connection
  router.get('/test', async (req, res) => {
    try {
      console.log('Testing Stripe connection...');
      console.log('Stripe key exists:', !!process.env.STRIPE_SECRET_KEY);
      console.log('Stripe key starts with:', process.env.STRIPE_SECRET_KEY?.substring(0, 10));

      const customers = await stripe.customers.list({ limit: 1 });
      res.json({
        success: true,
        message: 'Stripe connection successful!',
        customerCount: customers.data.length
      });
    } catch (error) {
      console.error('Stripe test error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Create Stripe Checkout Session for guest and authenticated users
  router.post('/create-checkout-session', async (req, res) => {
    try {
      console.log('🔄 Creating Stripe checkout session...');
      console.log('Request body:', JSON.stringify(req.body, null, 2));

      const schema = z.object({
        userId: z.number().nullable().optional(),
        cartItems: z.array(z.object({
          productId: z.number(),
          quantity: z.number().min(1)
        })).min(1),
        coinsUsed: z.number().optional(),
        paymentMethod: z.string().optional()
      });

      const { userId, cartItems, coinsUsed = 0, paymentMethod = 'stripe' } = schema.parse(req.body);
      console.log('✅ Parsed request data:', { userId, cartItems, coinsUsed, paymentMethod });

      // Validate cart items and fetch actual product prices from DB (security critical)
      const lineItems = [];
      let totalAmount = 0;

      for (const item of cartItems) {
        const product = await storage.getProductById(item.productId);
        if (!product) {
          return res.status(400).json({ error: `Product ${item.productId} not found` });
        }

        const itemTotal = product.price * item.quantity;
        totalAmount += itemTotal;

        lineItems.push({
          price_data: {
            currency: 'usd',
            product_data: {
              name: product.title,
              description: product.description || undefined,
              images: product.imageUrl ? [product.imageUrl] : undefined,
            },
            unit_amount: product.price, // Price in cents
          },
          quantity: item.quantity,
        });
      }

      // Apply coin discount to the total amount
      const coinsUsedValue = coinsUsed || 0; // DasWos coins are 1:1 with cents
      const finalAmount = Math.max(0, totalAmount - coinsUsedValue);

      console.log('💰 Payment calculation:', {
        originalTotal: totalAmount,
        coinsUsed: coinsUsedValue,
        finalStripeAmount: finalAmount
      });

      // If the final amount is 0 (fully paid with coins), we shouldn't create a Stripe session
      if (finalAmount === 0) {
        return res.status(400).json({
          error: 'Order fully paid with coins - no Stripe payment needed',
          totalAmount,
          coinsUsed: coinsUsedValue
        });
      }

      // Add coin discount as a line item if coins are used
      if (coinsUsedValue > 0) {
        lineItems.push({
          price_data: {
            currency: 'usd',
            product_data: {
              name: 'DasWos Coins Discount',
              description: `Applied ${(coinsUsedValue / 100).toFixed(2)} DasWos coins`,
            },
            unit_amount: -coinsUsedValue, // Negative amount for discount
          },
          quantity: 1,
        });
      }

      // Get user email if authenticated
      let customerEmail = undefined;
      if (userId) {
        const user = await storage.getUserById(userId);
        customerEmail = user?.email;
      }

      // Create Stripe Checkout Session
      console.log('💳 Creating Stripe session with:', {
        lineItems: lineItems.length,
        originalTotal: totalAmount,
        finalAmount,
        coinsUsed: coinsUsedValue,
        customerEmail,
        userId: userId ? String(userId) : 'guest'
      });

      const session = await stripe.checkout.sessions.create({
        mode: 'payment',
        line_items: lineItems,
        success_url: `${process.env.FRONTEND_URL || 'http://localhost:3003'}/checkout-success?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${process.env.FRONTEND_URL || 'http://localhost:3003'}/checkout-cancel`,
        customer_email: customerEmail,
        shipping_address_collection: {
          allowed_countries: ['US', 'CA', 'GB', 'AU'],
        },
        metadata: {
          userId: userId ? String(userId) : 'guest',
          cartItems: JSON.stringify(cartItems),
          coinsUsed: String(coinsUsed),
          paymentMethod: paymentMethod
        },
      });

      console.log('✅ Stripe session created:', session.id);

      res.json({
        success: true,
        sessionId: session.id,
        url: session.url
      });

    } catch (error) {
      console.error('Error creating Stripe checkout session:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: "Invalid checkout data", details: error.errors });
      }
      res.status(500).json({ error: "Failed to create checkout session" });
    }
  });

  // Create a Stripe customer
  router.post('/create-customer', async (req, res) => {
    try {
      const schema = z.object({
        email: z.string().email(),
        name: z.string()
      });

      const { email, name } = schema.parse(req.body);

      // Create a customer in Stripe
      const customerId = await createCustomer(email, name);

      res.json({ 
        success: true, 
        customerId 
      });
    } catch (error) {
      console.error('Error creating Stripe customer:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: "Invalid customer data", details: error.errors });
      }
      res.status(500).json({ error: "Failed to create Stripe customer" });
    }
  });

  // Create a Stripe subscription
  router.post('/create-subscription', async (req, res) => {
    try {
      const schema = z.object({
        customerId: z.string(),
        paymentMethodId: z.string(),
        planType: z.enum(['limited', 'unlimited', 'individual', 'family', 'standard']),
        billingCycle: z.enum(['monthly', 'annual'])
      });

      const { customerId, paymentMethodId, planType, billingCycle } = schema.parse(req.body);

      // For free plans, we don't need to create a subscription
      if (planType === 'limited' || planType === 'standard') {
        return res.json({
          success: true,
          subscriptionId: 'free_plan_no_subscription'
        });
      }

      // Get the price ID for the plan
      const priceId = getPriceId(planType, billingCycle);

      // Create metadata for the subscription
      const metadata = {
        planType,
        billingCycle
      };

      // Create a subscription in Stripe
      const subscription = await createSubscription(
        customerId,
        priceId,
        paymentMethodId,
        metadata
      );

      res.json({
        success: true,
        subscriptionId: subscription.id
      });
    } catch (error) {
      console.error('Error creating Stripe subscription:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: "Invalid subscription data", details: error.errors });
      }
      res.status(500).json({ error: "Failed to create Stripe subscription" });
    }
  });

  // Create a payment intent for one-time payments
  router.post('/create-payment-intent', async (req, res) => {
    try {
      const schema = z.object({
        amount: z.number().min(1), // Amount in cents
        currency: z.string().default('usd'),
        description: z.string().optional(),
        metadata: z.record(z.string()).optional()
      });

      const { amount, currency, description, metadata } = schema.parse(req.body);

      // TODO: Import and use Stripe to create payment intent
      // For now, return a mock response
      const paymentIntent = {
        id: 'pi_mock_' + Date.now(),
        client_secret: 'pi_mock_secret_' + Date.now(),
        amount,
        currency,
        status: 'requires_payment_method'
      };

      res.json({
        success: true,
        paymentIntent
      });
    } catch (error) {
      console.error('Error creating payment intent:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: "Invalid payment data", details: error.errors });
      }
      res.status(500).json({ error: "Failed to create payment intent" });
    }
  });

  // Stripe webhook handler
  router.post('/webhook', async (req, res) => {
    const signature = req.headers['stripe-signature'];

    if (!signature) {
      return res.status(400).json({ error: 'Missing Stripe signature' });
    }

    try {
      // Verify webhook signature
      const event = stripe.webhooks.constructEvent(
        req.body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET!
      );

      // Handle checkout.session.completed event
      if (event.type === 'checkout.session.completed') {
        const session = event.data.object as Stripe.Checkout.Session;

        // Extract metadata
        const rawUserId = session.metadata?.userId;
        const cartItemsString = session.metadata?.cartItems;
        const coinsUsedString = session.metadata?.coinsUsed;
        const paymentMethodFromMetadata = session.metadata?.paymentMethod;

        if (!cartItemsString) {
          console.error('No cart items in session metadata');
          return res.json({ received: true });
        }

        const cartItems = JSON.parse(cartItemsString);
        const coinsUsed = coinsUsedString ? parseInt(coinsUsedString) : 0;

        // Determine if guest or authenticated user
        const actualUserId = rawUserId === 'guest' ? null : parseInt(rawUserId!);

        // Get customer details from Stripe
        const customerEmail = session.customer_details?.email;
        const stripeShippingAddress = session.shipping_details?.address;

        // Calculate the original total amount (before coin discount)
        const originalTotal = cartItems.reduce((sum: number, item: any) => {
          return sum + (item.price * item.quantity);
        }, 0);

        // Create order in database
        try {
          const order = await storage.createOrder({
            userId: actualUserId,
            totalAmount: originalTotal / 100, // Convert from cents to dollars (original total, not Stripe amount)
            status: 'confirmed',
            shippingAddress: JSON.stringify(stripeShippingAddress || {}),
            billingAddress: JSON.stringify(session.customer_details?.address || stripeShippingAddress || {}),
            paymentMethod: paymentMethodFromMetadata || 'stripe_checkout',
            paymentReference: session.payment_intent as string,
            notes: `Guest email: ${customerEmail || 'N/A'}, Coins used: ${coinsUsed}, Stripe amount: $${(session.amount_total! / 100).toFixed(2)}, Original total: $${(originalTotal / 100).toFixed(2)}`
          });

          // Add order items
          for (const item of cartItems) {
            const product = await storage.getProductById(item.productId);
            if (product) {
              await storage.addOrderItem({
                orderId: order.id,
                productId: item.productId,
                quantity: item.quantity,
                priceAtPurchase: product.price,
                itemNameSnapshot: product.title
              });

              // Update product quantity if available
              // TODO: Implement updateProductQuantity in storage
            }
          }

          // Clear cart for authenticated users
          if (actualUserId) {
            // TODO: Implement clearUserCart in storage
            console.log(`Order ${order.id} created for user ${actualUserId}`);
          } else {
            console.log(`Guest order ${order.id} created for ${customerEmail}`);
          }

        } catch (orderError) {
          console.error('Error creating order from webhook:', orderError);
        }
      }

      res.json({ received: true });
    } catch (error) {
      console.error('Error handling Stripe webhook:', error);
      res.status(400).json({ error: 'Webhook error' });
    }
  });

  return router;
}
