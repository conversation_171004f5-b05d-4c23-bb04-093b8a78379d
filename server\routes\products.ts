import express from 'express';
import { IStorage } from '../storage';
import multer from 'multer';
import { db } from '../db';
import { categories } from '../../shared/schema1';
import { eq } from 'drizzle-orm';

// Configure multer for handling FormData (including file uploads)
const upload = multer({
  storage: multer.memoryStorage(), // Store files in memory for processing
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    fieldSize: 10 * 1024 * 1024 // 10MB limit for text fields
  },
  fileFilter: (req, file, cb) => {
    // Accept only images
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

function createProductRoutes(storage: IStorage) {
  const router = express.Router();

  // Get all products (main search endpoint)
  router.get('/', async (req, res) => {
    try {
      const sphere = req.query.sphere as string || "safesphere";
      const query = req.query.q as string || "";
      const isBulkBuy = req.query.bulk === 'true';
      const categoryName = req.query.category as string;

      // SuperSafe Mode parameters
      const superSafeEnabled = req.query.superSafeEnabled === 'true';
      const blockGambling = req.query.blockGambling === 'true';
      const blockAdultContent = req.query.blockAdultContent === 'true';

      // If SuperSafe Mode is enabled and OpenSphere is blocked, force SafeSphere
      let effectiveSphere = sphere;
      if (superSafeEnabled && req.query.blockOpenSphere === 'true' && sphere === 'opensphere') {
        effectiveSphere = 'safesphere';
        console.log('SuperSafe Mode: Forcing SafeSphere due to OpenSphere blocking');
      }

      // If bulkBuy is requested, modify the sphere to include bulkbuy filter
      if (isBulkBuy) {
        effectiveSphere = effectiveSphere === 'safesphere' ? 'bulkbuy-safe' : 'bulkbuy-open';
      }

      console.log(`Products API request - sphere: ${sphere}, query: "${query}", bulkBuy: ${isBulkBuy}, effectiveSphere: ${effectiveSphere}, superSafeEnabled: ${superSafeEnabled}, category: ${categoryName || 'none'}`);

      // Get products based on the effective sphere and category
      let products;
      if (categoryName) {
        // If a category is specified, get products by category
        products = await storage.getProductsByCategory(categoryName);
      } else {
        // Otherwise get by general query
        products = await storage.getProducts(effectiveSphere, query);
      }

      // CRITICAL: Advanced deduplication to prevent perceived duplicates
      const originalCount = products.length;
      products = deduplicateSearchResults(products, query);
      console.log(`🔧 Products route: Advanced deduplication ${originalCount} -> ${products.length} products`);

      // Apply SuperSafe Mode filters if enabled
      if (superSafeEnabled) {
        // Filter out gambling-related products if blockGambling is enabled
        if (blockGambling) {
          const gamblingKeywords = ['gambling', 'casino', 'poker', 'betting', 'lottery', 'slot', 'roulette'];
          products = products.filter(product => {
            const title = product.title.toLowerCase();
            const description = product.description.toLowerCase();
            const tags = product.tags.map(tag => tag.toLowerCase());

            // Check if any gambling keywords are in the title, description, or tags
            return !gamblingKeywords.some(keyword =>
              title.includes(keyword) ||
              description.includes(keyword) ||
              tags.includes(keyword)
            );
          });
          console.log(`SuperSafe Mode: Filtered out gambling-related products, ${products.length} remaining`);
        }

        // Filter out adult content if blockAdultContent is enabled
        if (blockAdultContent) {
          const adultKeywords = ['adult', 'mature', 'xxx', 'nsfw', 'explicit', 'erotic'];
          products = products.filter(product => {
            const title = product.title.toLowerCase();
            const description = product.description.toLowerCase();
            const tags = product.tags.map(tag => tag.toLowerCase());

            // Check if any adult keywords are in the title, description, or tags
            return !adultKeywords.some(keyword =>
              title.includes(keyword) ||
              description.includes(keyword) ||
              tags.includes(keyword)
            );
          });
          console.log(`SuperSafe Mode: Filtered out adult content, ${products.length} remaining`);
        }
      }

      // Track search history if user is authenticated
      if (req.isAuthenticated() && query) {
        try {
          // Get category ID if category name is provided
          let categoryId = null;
          if (categoryName) {
            const categories = await storage.getCategoryIdsByNames([categoryName]);
            if (categories.length > 0) {
              categoryId = categories[0];
            }
          }

          // Add to search history
          await storage.addUserSearchHistory(req.user.id, query, categoryId);
          console.log(`Added search query "${query}" to user ${req.user.id}'s history`);
        } catch (historyError) {
          console.error('Error tracking search history:', historyError);
          // Continue with the response even if history tracking fails
        }
      }

      res.json(products);
    } catch (error) {
      console.error('Error fetching products:', error);
      res.status(500).json({ error: 'Failed to fetch products' });
    }
  });

  // Get a product by ID
  router.get('/:id', async (req, res) => {
    try {
      const productId = parseInt(req.params.id);
      if (isNaN(productId)) {
        return res.status(400).json({ error: "Invalid product ID" });
      }

      const product = await storage.getProductById(productId);
      if (!product) {
        return res.status(404).json({ error: "Product not found" });
      }

      // Track product click in search history if user is authenticated
      if (req.isAuthenticated()) {
        try {
          // Get the most recent search query for this user
          const searchHistory = await storage.getUserSearchHistory(req.user.id, 1);

          if (searchHistory.length > 0) {
            const latestSearch = searchHistory[0];

            // Update the search history with the clicked product
            await storage.addUserSearchHistory(
              req.user.id,
              latestSearch.searchQuery,
              product.categoryId,
              productId
            );

            console.log(`Updated search history for user ${req.user.id} with clicked product ${productId}`);
          }
        } catch (historyError) {
          console.error('Error tracking product click:', historyError);
          // Continue with the response even if history tracking fails
        }
      }

      res.json(product);
    } catch (error) {
      console.error('Error fetching product:', error);
      res.status(500).json({ error: 'Failed to fetch product' });
    }
  });

  // Get related products
  router.get('/:id/related', async (req, res) => {
    try {
      const id = Number(req.params.id);
      const product = await storage.getProductById(id);

      if (!product) {
        return res.status(404).json({ error: 'Product not found' });
      }

      const relatedProducts = await storage.getRelatedProducts(product.tags, id);
      res.json(relatedProducts);
    } catch (error) {
      console.error('Error fetching related products:', error);
      res.status(500).json({ error: 'Failed to fetch related products' });
    }
  });

  // Get products by category
  router.get('/category/:category', async (req, res) => {
    try {
      const category = req.params.category;
      const products = await storage.getProductsByCategory(category);
      res.json(products);
    } catch (error) {
      console.error('Error fetching products by category:', error);
      res.status(500).json({ error: 'Failed to fetch products by category' });
    }
  });

  // Create a new product
  router.post('/', upload.single('image'), async (req, res) => {
    try {
      // Ensure user is authorized to create products
      if (!req.session?.passport?.user) {
        console.log('No user ID in session:', req.session);
        return res.status(401).json({ error: 'Unauthorized' });
      }

      const userId = req.session.passport.user;
      console.log('Creating product for user ID:', userId);
      console.log('Request body received:', JSON.stringify(req.body, null, 2));
      console.log('Request file received:', req.file ? {
        fieldname: req.file.fieldname,
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size
      } : 'No file');
      console.log('Request content-type:', req.headers['content-type']);
      console.log('Request method:', req.method);

      // Get the user to assign seller details
      const user = await storage.getUser(userId);
      if (!user) {
        console.log('User not found in database:', userId);
        return res.status(404).json({ error: 'User not found' });
      }

      // Use the trust score stored in the database (calculated by the rating system)
      // This follows the SELLER_TRUST_SCORE.md specification
      const trustScore = user.trustScore;

      console.log('Using stored trust score for product creation:', {
        userId: user.id,
        trustScore,
        isSeller: user.isSeller,
        identityVerified: user.identityVerified
      });

      // Process image file if uploaded
      let imageUrl = req.body.imageUrl || '';
      if (req.file) {
        // Convert uploaded file to base64 for storage
        // In production, you'd upload to S3/CloudFlare/etc.
        const base64Image = `data:${req.file.mimetype};base64,${req.file.buffer.toString('base64')}`;
        imageUrl = base64Image;
        console.log('Processed uploaded image, size:', req.file.size, 'bytes');
      }

      // Process tags - handle both string and array formats
      let tags = req.body.tags || [];
      if (typeof tags === 'string') {
        try {
          // Try to parse as JSON first
          tags = JSON.parse(tags);
        } catch {
          // If not JSON, split by comma
          tags = tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
        }
      }
      if (!Array.isArray(tags)) {
        tags = [];
      }

      // Convert price to number (in cents)
      let priceInCents = 0;
      if (req.body.price) {
        const priceValue = typeof req.body.price === 'string' ? parseFloat(req.body.price) : req.body.price;
        priceInCents = Math.round(priceValue * 100); // Convert to cents
      }

      // Convert quantity to number
      const quantity = req.body.quantity ? parseInt(req.body.quantity, 10) : 1;

      // Handle category - convert category ID to proper integer
      let categoryId = null;
      if (req.body.category) {
        try {
          // The frontend now sends category IDs as strings, convert to integer
          const categoryIdNum = parseInt(req.body.category, 10);
          if (!isNaN(categoryIdNum)) {
            // Verify the category exists in the database
            const categoryExists = await db.select({ id: categories.id })
              .from(categories)
              .where(eq(categories.id, categoryIdNum))
              .limit(1);

            if (categoryExists.length > 0) {
              categoryId = categoryIdNum;
              console.log(`✅ Valid category ID: ${categoryId}`);
            } else {
              console.log(`⚠️ Category ID ${categoryIdNum} not found in database`);
            }
          }
        } catch (error) {
          console.error('Error processing category:', error);
        }
      }

      // Create the product data with proper types and field names
      const productData = {
        title: req.body.title || '',
        description: req.body.description || '',
        price: priceInCents,
        imageUrl: imageUrl,
        sellerId: user.id,
        sellerName: user.fullName,
        sellerVerified: user.isSeller,
        sellerType: 'merchant',
        trustScore: trustScore,
        // Copy seller's identity verification status to product (for SafeSphere filtering)
        identityVerified: user.identityVerified,
        identityVerificationStatus: user.identityVerificationStatus,
        tags: tags,
        shipping: req.body.shipping || 'standard',
        originalPrice: null,
        discount: null,
        verifiedSince: null,
        warning: null,
        isBulkBuy: req.body.isBulkBuy === 'true' || req.body.isBulkBuy === true,
        bulkMinimumQuantity: null,
        bulkDiscountRate: null,
        imageDescription: null,
        categoryId: categoryId,
        aiAttributes: {},
        searchVector: '',
        // Product status and inventory management
        status: 'active', // Products go live immediately when created
        quantity: quantity,
        soldQuantity: 0,
        // Note: createdAt and updatedAt will be set automatically by the database
      };

      console.log('Creating product with data:', productData);

      try {
        const product = await storage.createProduct(productData);
        console.log('Product created successfully:', product);

        // Automatically make user a seller when they list their first item
        if (!user.isSeller) {
          console.log(`Making user ${user.id} a seller after listing their first item`);
          try {
            await storage.updateUserSellerStatus(user.id, true);
            console.log(`Successfully updated user ${user.id} to seller status`);
          } catch (sellerUpdateError) {
            console.error('Error updating user seller status:', sellerUpdateError);
            // Don't fail the product creation if seller status update fails
          }
        }

        res.status(201).json(product);
      } catch (dbError) {
        console.error('Database error creating product:', dbError);
        console.error('Product data that failed:', JSON.stringify(productData, null, 2));
        res.status(500).json({
          message: 'Failed to create product in database',
          error: dbError instanceof Error ? dbError.message : 'Unknown database error'
        });
        return;
      }
    } catch (error) {
      console.error('Error creating product:', error);
      res.status(500).json({ error: 'Failed to create product' });
    }
  });

  // Get products by seller ID
  router.get('/seller/:sellerId', async (req, res) => {
    try {
      const sellerId = Number(req.params.sellerId);
      const products = await storage.getProductsBySellerId(sellerId);

      // CRITICAL: Remove duplicates based on product ID
      const uniqueProducts = Array.from(new Map(products.map(product => [product.id, product])).values());
      console.log(`🔧 Seller products route: Removed ${products.length - uniqueProducts.length} duplicate products for seller ${sellerId} (${products.length} -> ${uniqueProducts.length})`);

      res.json(uniqueProducts);
    } catch (error) {
      console.error('Error fetching products by seller:', error);
      res.status(500).json({ error: 'Failed to fetch products by seller' });
    }
  });

  // Alternative route for /api/sellers/:id/products (for compatibility)
  router.get('/sellers/:id/products', async (req, res) => {
    try {
      const sellerId = parseInt(req.params.id);
      if (isNaN(sellerId)) {
        return res.status(400).json({ error: "Invalid seller ID" });
      }

      const products = await storage.getProductsBySellerId(sellerId);

      // CRITICAL: Remove duplicates based on product ID
      const uniqueProducts = Array.from(new Map(products.map(product => [product.id, product])).values());
      console.log(`🔧 Sellers products route: Removed ${products.length - uniqueProducts.length} duplicate products for seller ${sellerId} (${products.length} -> ${uniqueProducts.length})`);

      res.json(uniqueProducts);
    } catch (error) {
      console.error('Error fetching seller products:', error);
      res.status(500).json({ error: "Failed to fetch seller products" });
    }
  });

  return router;
}

/**
 * Advanced deduplication to prevent perceived duplicates in search results
 * This addresses the broader issue of similar products appearing as duplicates to users
 */
function deduplicateSearchResults(products: any[], searchQuery: string): any[] {
  if (!products || products.length === 0) {
    return products;
  }

  console.log(`🔧 Starting advanced deduplication for ${products.length} products`);

  // Step 1: Remove exact ID duplicates (basic safety net)
  const uniqueById = Array.from(new Map(products.map(product => [product.id, product])).values());

  // Step 2: Group similar products and keep the best representative from each group
  const productGroups = new Map<string, any[]>();

  for (const product of uniqueById) {
    // Create a normalized key for grouping similar products
    const groupKey = createProductGroupKey(product);

    if (!productGroups.has(groupKey)) {
      productGroups.set(groupKey, []);
    }
    productGroups.get(groupKey)!.push(product);
  }

  console.log(`🔧 Grouped ${uniqueById.length} products into ${productGroups.size} groups`);

  // Step 3: From each group, select the best representative
  const deduplicatedProducts: any[] = [];

  for (const [groupKey, groupProducts] of productGroups) {
    if (groupProducts.length === 1) {
      // Single product in group, keep it
      deduplicatedProducts.push(groupProducts[0]);
    } else {
      // Multiple products in group, select the best one
      console.log(`🔧 Group "${groupKey}" has ${groupProducts.length} similar products, selecting best`);

      const bestProduct = selectBestProductFromGroup(groupProducts, searchQuery);
      deduplicatedProducts.push(bestProduct);
    }
  }

  console.log(`🔧 Advanced deduplication: ${products.length} -> ${deduplicatedProducts.length} products`);

  return deduplicatedProducts;
}

/**
 * Create a normalized key for grouping similar products
 */
function createProductGroupKey(product: any): string {
  // Normalize the title by removing common variations
  let normalizedTitle = product.title?.toLowerCase() || '';

  // Remove common prefixes/suffixes that don't change the core product
  const commonPrefixes = ['premium', 'high-quality', 'professional', 'deluxe', 'luxury', 'classic', 'modern', 'vintage', 'new', 'original'];
  const commonSuffixes = ['edition', 'version', 'model', 'style', 'design', 'collection', 'series'];

  // Remove common words that don't affect product identity
  const wordsToRemove = [...commonPrefixes, ...commonSuffixes, 'quality', 'grade', 'top', 'best', 'super', 'ultra', 'mega', 'pro'];

  // Split title into words and filter out common words
  const titleWords = normalizedTitle.split(/\s+/).filter(word => {
    return word.length > 2 && !wordsToRemove.includes(word);
  });

  // Sort words to handle different word orders
  const coreTitle = titleWords.sort().join(' ');

  // Create a key that groups similar products
  // Include price range to avoid grouping products with very different prices
  const priceRange = Math.floor((product.price || 0) / 1000) * 1000; // Group by $10 ranges (price in cents)

  // Include category to avoid grouping unrelated products
  const category = product.categoryId || 'unknown';

  return `${coreTitle}|${priceRange}|${category}`;
}

/**
 * Select the best product from a group of similar products
 */
function selectBestProductFromGroup(products: any[], searchQuery: string): any {
  if (products.length === 1) {
    return products[0];
  }

  // Score each product based on multiple criteria
  const scoredProducts = products.map(product => ({
    product,
    score: calculateProductQualityScore(product, searchQuery)
  }));

  // Sort by score (highest first) and return the best
  scoredProducts.sort((a, b) => b.score - a.score);

  console.log(`🔧 Selected product "${scoredProducts[0].product.title}" (score: ${scoredProducts[0].score}) from group of ${products.length}`);

  return scoredProducts[0].product;
}

/**
 * Calculate a comprehensive quality score for product selection
 */
function calculateProductQualityScore(product: any, searchQuery: string): number {
  let score = 0;

  // Base quality indicators
  score += (product.trustScore || 0) * 0.5; // Trust score (0-100) * 0.5 = 0-50 points
  score += product.sellerVerified ? 20 : 0; // Verified seller bonus
  score += product.identityVerified ? 15 : 0; // Identity verified bonus

  // Search relevance
  if (searchQuery) {
    score += calculateRelevanceScore(product, searchQuery) * 0.3; // Relevance bonus
  }

  // Data completeness
  score += product.title ? 10 : 0;
  score += product.description ? 10 : 0;
  score += product.imageUrl ? 5 : 0;
  score += (product.tags && product.tags.length > 0) ? 5 : 0;
  score += product.categoryId ? 5 : 0;

  // Recency bonus (newer products get slight preference)
  if (product.createdAt) {
    const daysSinceCreation = (Date.now() - new Date(product.createdAt).getTime()) / (1000 * 60 * 60 * 24);
    score += Math.max(0, 10 - daysSinceCreation * 0.1); // Up to 10 points for recent products
  }

  // Price reasonableness (avoid extremely high or low prices that might be errors)
  const price = product.price || 0;
  if (price > 100 && price < 100000) { // Between $1 and $1000
    score += 5;
  }

  return score;
}

/**
 * Calculate relevance score for a product based on search query
 */
function calculateRelevanceScore(product: any, searchQuery: string): number {
  if (!searchQuery || !searchQuery.trim()) {
    return 0;
  }

  let score = 0;
  const query = searchQuery.toLowerCase().trim();
  const searchTerms = query.split(' ').filter(term => term.length > 0);

  const title = product.title?.toLowerCase() || '';
  const description = product.description?.toLowerCase() || '';
  const tags = product.tags?.join(' ').toLowerCase() || '';
  const sellerName = product.sellerName?.toLowerCase() || '';

  searchTerms.forEach(term => {
    // Exact title match gets highest score
    if (title === term) {
      score += 100;
    }
    // Title contains term gets high score
    else if (title.includes(term)) {
      score += 50;
    }

    // Description contains term gets medium score
    if (description.includes(term)) {
      score += 20;
    }

    // Tags contain term gets medium score
    if (tags.includes(term)) {
      score += 15;
    }

    // Seller name contains term gets low score
    if (sellerName.includes(term)) {
      score += 5;
    }
  });

  // Boost score for products with higher trust scores
  if (product.trustScore) {
    score += product.trustScore * 0.1;
  }

  // Boost score for verified sellers
  if (product.sellerVerified) {
    score += 10;
  }

  return score;
}

export { createProductRoutes };