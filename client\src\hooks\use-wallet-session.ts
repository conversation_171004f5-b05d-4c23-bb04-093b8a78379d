import { useState, useEffect, useCallback } from 'react';

interface WalletSessionData {
  walletId: string;
  authenticatedAt: number;
  lastAccessed: number;
}

interface WalletSessionManager {
  authenticatedWallets: Set<string>;
  addAuthenticatedWallet: (walletId: string) => void;
  removeAuthenticatedWallet: (walletId: string) => void;
  isWalletAuthenticated: (walletId: string) => boolean;
  clearAllSessions: () => void;
  getSessionData: () => WalletSessionData[];
}

const SESSION_STORAGE_KEY = 'daswos_wallet_sessions';
const SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours

export function useWalletSession(): WalletSessionManager {
  const [authenticatedWallets, setAuthenticatedWallets] = useState<Set<string>>(new Set());

  // Load sessions from localStorage on mount
  useEffect(() => {
    const loadSessions = () => {
      try {
        const stored = localStorage.getItem(SESSION_STORAGE_KEY);
        if (stored) {
          const sessions: WalletSessionData[] = JSON.parse(stored);
          const now = Date.now();
          
          // Filter out expired sessions
          const validSessions = sessions.filter(
            session => (now - session.authenticatedAt) < SESSION_TIMEOUT
          );
          
          // Update localStorage with valid sessions only
          localStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(validSessions));
          
          // Set authenticated wallets
          const walletIds = new Set(validSessions.map(s => s.walletId));
          setAuthenticatedWallets(walletIds);
        }
      } catch (error) {
        console.error('Error loading wallet sessions:', error);
        localStorage.removeItem(SESSION_STORAGE_KEY);
      }
    };

    loadSessions();
  }, []);

  // Save sessions to localStorage
  const saveSessions = useCallback((sessions: WalletSessionData[]) => {
    try {
      localStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(sessions));
    } catch (error) {
      console.error('Error saving wallet sessions:', error);
    }
  }, []);

  // Add authenticated wallet
  const addAuthenticatedWallet = useCallback((walletId: string) => {
    const now = Date.now();
    
    // Get current sessions
    const stored = localStorage.getItem(SESSION_STORAGE_KEY);
    let sessions: WalletSessionData[] = stored ? JSON.parse(stored) : [];
    
    // Remove existing session for this wallet (if any)
    sessions = sessions.filter(s => s.walletId !== walletId);
    
    // Add new session
    sessions.push({
      walletId,
      authenticatedAt: now,
      lastAccessed: now
    });
    
    // Save to localStorage
    saveSessions(sessions);
    
    // Update state
    setAuthenticatedWallets(prev => new Set([...prev, walletId]));
  }, [saveSessions]);

  // Remove authenticated wallet
  const removeAuthenticatedWallet = useCallback((walletId: string) => {
    // Get current sessions
    const stored = localStorage.getItem(SESSION_STORAGE_KEY);
    if (stored) {
      let sessions: WalletSessionData[] = JSON.parse(stored);
      
      // Remove session for this wallet
      sessions = sessions.filter(s => s.walletId !== walletId);
      
      // Save to localStorage
      saveSessions(sessions);
    }
    
    // Update state
    setAuthenticatedWallets(prev => {
      const newSet = new Set(prev);
      newSet.delete(walletId);
      return newSet;
    });
  }, [saveSessions]);

  // Check if wallet is authenticated
  const isWalletAuthenticated = useCallback((walletId: string) => {
    return authenticatedWallets.has(walletId);
  }, [authenticatedWallets]);

  // Clear all sessions
  const clearAllSessions = useCallback(() => {
    localStorage.removeItem(SESSION_STORAGE_KEY);
    setAuthenticatedWallets(new Set());
  }, []);

  // Get session data
  const getSessionData = useCallback((): WalletSessionData[] => {
    try {
      const stored = localStorage.getItem(SESSION_STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error getting session data:', error);
      return [];
    }
  }, []);

  // Update last accessed time for a wallet
  const updateLastAccessed = useCallback((walletId: string) => {
    if (!authenticatedWallets.has(walletId)) return;
    
    const stored = localStorage.getItem(SESSION_STORAGE_KEY);
    if (stored) {
      let sessions: WalletSessionData[] = JSON.parse(stored);
      
      // Update last accessed time
      sessions = sessions.map(session => 
        session.walletId === walletId 
          ? { ...session, lastAccessed: Date.now() }
          : session
      );
      
      saveSessions(sessions);
    }
  }, [authenticatedWallets, saveSessions]);

  // Auto-cleanup expired sessions every 5 minutes
  useEffect(() => {
    const cleanup = () => {
      const stored = localStorage.getItem(SESSION_STORAGE_KEY);
      if (stored) {
        const sessions: WalletSessionData[] = JSON.parse(stored);
        const now = Date.now();
        
        const validSessions = sessions.filter(
          session => (now - session.authenticatedAt) < SESSION_TIMEOUT
        );
        
        if (validSessions.length !== sessions.length) {
          saveSessions(validSessions);
          const walletIds = new Set(validSessions.map(s => s.walletId));
          setAuthenticatedWallets(walletIds);
        }
      }
    };

    const interval = setInterval(cleanup, 5 * 60 * 1000); // 5 minutes
    return () => clearInterval(interval);
  }, [saveSessions]);

  return {
    authenticatedWallets,
    addAuthenticatedWallet,
    removeAuthenticatedWallet,
    isWalletAuthenticated,
    clearAllSessions,
    getSessionData
  };
}
