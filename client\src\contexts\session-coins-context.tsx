import React, { createContext, useContext, useState, useEffect } from 'react';

interface SessionCoinsContextType {
  sessionCoins: number;
  addSessionCoins: (amount: number) => void;
  spendSessionCoins: (amount: number) => boolean;
  clearSessionCoins: () => void;
  hasSessionCoins: boolean;
}

const SessionCoinsContext = createContext<SessionCoinsContextType | undefined>(undefined);

export const useSessionCoins = () => {
  const context = useContext(SessionCoinsContext);
  if (!context) {
    throw new Error('useSessionCoins must be used within a SessionCoinsProvider');
  }
  return context;
};

interface SessionCoinsProviderProps {
  children: React.ReactNode;
}

export const SessionCoinsProvider: React.FC<SessionCoinsProviderProps> = ({ children }) => {
  const [sessionCoins, setSessionCoins] = useState<number>(() => {
    // Load from session storage on initialization
    const today = new Date().toISOString().split('T')[0];
    const stored = sessionStorage.getItem(`sessionCoins_${today}`);
    return stored ? parseInt(stored, 10) : 0;
  });

  // Save to session storage whenever coins change
  useEffect(() => {
    const today = new Date().toISOString().split('T')[0];
    sessionStorage.setItem(`sessionCoins_${today}`, sessionCoins.toString());
    console.log('💰 Session coins updated:', sessionCoins);
  }, [sessionCoins]);

  // Clear coins from previous days on app start
  useEffect(() => {
    const today = new Date().toISOString().split('T')[0];
    const keys = Object.keys(sessionStorage);
    keys.forEach(key => {
      if (key.startsWith('sessionCoins_') && !key.includes(today)) {
        sessionStorage.removeItem(key);
        console.log('🗑️ Cleared old session coins:', key);
      }
    });
  }, []);

  const addSessionCoins = (amount: number) => {
    setSessionCoins(prev => {
      const newAmount = prev + amount;
      console.log(`💰 Added ${amount} session coins (${amount} DasWos coins). Total: ${newAmount}`);
      return newAmount;
    });
  };

  const spendSessionCoins = (amount: number): boolean => {
    if (sessionCoins >= amount) {
      setSessionCoins(prev => {
        const newAmount = prev - amount;
        console.log(`💸 Spent ${amount} session coins ($${amount}). Remaining: ${newAmount}`);
        return newAmount;
      });
      return true;
    }
    console.log(`❌ Insufficient session coins. Need: ${amount} ($${amount}), Have: ${sessionCoins} ($${sessionCoins})`);
    return false;
  };

  const clearSessionCoins = () => {
    setSessionCoins(0);
    const today = new Date().toISOString().split('T')[0];
    sessionStorage.removeItem(`sessionCoins_${today}`);
    console.log('🗑️ Cleared all session coins');
  };

  const hasSessionCoins = sessionCoins > 0;

  return (
    <SessionCoinsContext.Provider value={{
      sessionCoins,
      addSessionCoins,
      spendSessionCoins,
      clearSessionCoins,
      hasSessionCoins
    }}>
      {children}
    </SessionCoinsContext.Provider>
  );
};
