// =====================================================
// SCHEMA 2: WALLET DATABASE SCHEMA
// =====================================================
// This file contains the TypeScript schema definitions for the wallet database
// Corresponds to unified_schema2.sql
// 
// Contains: Wallets, wallet sessions, wallet connections, etc.
// Database: Supabase wallet database
// =====================================================

import { pgTable, text, serial, integer, boolean, varchar, jsonb, timestamp, decimal, real, pgEnum, primaryKey, pgSchema, unique, date, uuid } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { never, z } from "zod";
import { relations } from "drizzle-orm";
import { sql } from "drizzle-orm";

// Wallets table - Main wallet authentication and management
export const wallets = pgTable("wallets", {
  id: uuid("id").primaryKey().defaultRandom(),
  walletId: text("wallet_id").notNull().unique(), // User-chosen wallet ID
  passwordHash: text("password_hash").notNull(), // Scrypt hashed password

  // Multi-wallet support
  walletType: text("wallet_type").default("standard").notNull(), // 'standard', 'children', 'business', 'savings', 'shared'
  walletName: text("wallet_name"), // User-friendly name for the wallet
  walletDescription: text("wallet_description"), // Optional description

  // Children's wallet specific fields
  isChildrenWallet: boolean("is_children_wallet").default(false).notNull(),
  parentWalletId: uuid("parent_wallet_id"), // Reference to parent wallet for children's wallets
  ageRestriction: integer("age_restriction"), // Age restriction for children's wallets (e.g., 13, 16, 18)
  spendingLimitDaily: integer("spending_limit_daily").default(0), // Daily spending limit in cents for children's wallets
  spendingLimitMonthly: integer("spending_limit_monthly").default(0), // Monthly spending limit in cents
  allowedCategories: text("allowed_categories").array().default([]), // Allowed product categories for children's wallets
  restrictedFeatures: text("restricted_features").array().default([]), // Restricted features (e.g., 'opensphere', 'gambling', 'adult_content')

  // REMOVED: balance field - balances are now stored in user accounts in main databases
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  isActive: boolean("is_active").default(true).notNull(),
  lastAccessed: timestamp("last_accessed").defaultNow().notNull(),

  // Security fields
  failedLoginAttempts: integer("failed_login_attempts").default(0).notNull(),
  lockedUntil: timestamp("locked_until"),

  // Metadata
  creationIp: text("creation_ip"), // INET type not available in drizzle, using text
  lastLoginIp: text("last_login_ip"), // INET type not available in drizzle, using text
});

// Wallet Sessions table - Active wallet sessions for authentication
export const walletSessions = pgTable("wallet_sessions", {
  id: uuid("id").primaryKey().defaultRandom(),
  walletId: uuid("wallet_id").notNull().references(() => wallets.id, { onDelete: "cascade" }),
  sessionToken: text("session_token").notNull().unique(),
  deviceInfo: jsonb("device_info").default({}),
  ipAddress: text("ip_address"), // INET type not available in drizzle, using text
  userAgent: text("user_agent"),
  isActive: boolean("is_active").default(true).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  expiresAt: timestamp("expires_at").notNull(),
  lastActivity: timestamp("last_activity").defaultNow().notNull(),
});

// Transactions table - Audit log for wallet interface access and transaction attempts
export const transactions = pgTable("transactions", {
  id: uuid("id").primaryKey().defaultRandom(),
  walletId: uuid("wallet_id").notNull().references(() => wallets.id, { onDelete: "cascade" }),
  transactionType: text("transaction_type").notNull(), // 'interface_access', 'balance_check', 'purchase_attempt', 'spend_attempt'
  amount: decimal("amount", { precision: 15, scale: 2 }), // Amount involved (for reference only)
  description: text("description"),
  referenceId: text("reference_id"), // Reference to external transaction (purchase ID, etc.)
  referenceType: text("reference_type"), // 'purchase', 'spend', 'balance_check', 'user_login'

  // Connection to main database
  databaseName: text("database_name"), // Which main database this transaction relates to
  userId: integer("user_id"), // User ID in the main database

  // Metadata
  createdAt: timestamp("created_at").defaultNow().notNull(),
  processedAt: timestamp("processed_at").defaultNow().notNull(),
  ipAddress: text("ip_address"), // INET type not available in drizzle, using text
  userAgent: text("user_agent"),

  // Status
  status: text("status").default("completed").notNull(), // 'pending', 'completed', 'failed', 'cancelled'
});

// Wallet Connections table - Link wallets to user accounts in main databases
export const walletConnections = pgTable("wallet_connections", {
  id: uuid("id").primaryKey().defaultRandom(),
  walletId: uuid("wallet_id").notNull().references(() => wallets.id, { onDelete: "cascade" }),

  // Connection to main database
  databaseName: text("database_name").notNull(), // 'daswos-18', 'current-brobot-1'
  userId: integer("user_id").notNull(), // User ID in the main database (now required)
  username: text("username"), // Username in the main database (for reference)

  // Multi-wallet support
  walletOrder: integer("wallet_order").default(1).notNull(), // Order of wallets for this user (1-5)
  walletNickname: text("wallet_nickname"), // User-defined nickname for this wallet connection

  // Connection metadata
  connectedAt: timestamp("connected_at").defaultNow().notNull(),
  lastUsed: timestamp("last_used").defaultNow().notNull(),
  isPrimary: boolean("is_primary").default(false).notNull(), // Is this the primary wallet for this user?
  isActive: boolean("is_active").default(true).notNull(), // Is this connection currently active?
}, (table) => ({
  // Prevent duplicate connections
  uniqueConnection: unique("unique_connection").on(table.databaseName, table.userId, table.walletId),
  // Ensure unique wallet order per user
  uniqueWalletOrder: unique("unique_wallet_order").on(table.databaseName, table.userId, table.walletOrder),
}));

// Wallet Spending Tracking table - Track spending for children's wallets and limits
export const walletSpendingTracking = pgTable("wallet_spending_tracking", {
  id: uuid("id").primaryKey().defaultRandom(),
  walletId: uuid("wallet_id").notNull().references(() => wallets.id, { onDelete: "cascade" }),

  // Spending period
  trackingDate: date("tracking_date").notNull(), // Date for daily tracking
  trackingMonth: text("tracking_month").notNull(), // YYYY-MM format for monthly tracking

  // Spending amounts (in cents)
  dailySpent: integer("daily_spent").default(0).notNull(),
  monthlySpent: integer("monthly_spent").default(0).notNull(),

  // Transaction count
  dailyTransactionCount: integer("daily_transaction_count").default(0).notNull(),
  monthlyTransactionCount: integer("monthly_transaction_count").default(0).notNull(),

  // Metadata
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
}, (table) => ({
  // One record per wallet per day
  uniqueWalletDate: unique("unique_wallet_date").on(table.walletId, table.trackingDate),
  // One record per wallet per month
  uniqueWalletMonth: unique("unique_wallet_month").on(table.walletId, table.trackingMonth),
}));

// Relations
export const walletsRelations = relations(wallets, ({ many, one }) => ({
  sessions: many(walletSessions),
  transactions: many(transactions),
  connections: many(walletConnections),
  spendingTracking: many(walletSpendingTracking),
  // Self-reference for children's wallets
  parentWallet: one(wallets, {
    fields: [wallets.parentWalletId],
    references: [wallets.id],
    relationName: "parentChild"
  }),
  childrenWallets: many(wallets, { relationName: "parentChild" }),
}));

export const walletSessionsRelations = relations(walletSessions, ({ one }) => ({
  wallet: one(wallets, {
    fields: [walletSessions.walletId],
    references: [wallets.id],
  }),
}));

export const transactionsRelations = relations(transactions, ({ one }) => ({
  wallet: one(wallets, {
    fields: [transactions.walletId],
    references: [wallets.id],
  }),
}));

export const walletConnectionsRelations = relations(walletConnections, ({ one }) => ({
  wallet: one(wallets, {
    fields: [walletConnections.walletId],
    references: [wallets.id],
  }),
}));

export const walletSpendingTrackingRelations = relations(walletSpendingTracking, ({ one }) => ({
  wallet: one(wallets, {
    fields: [walletSpendingTracking.walletId],
    references: [wallets.id],
  }),
}));

// Insert schemas
export const insertWalletSchema = createInsertSchema(wallets).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  lastAccessed: true,
  failedLoginAttempts: true,
  lockedUntil: true,
});

export const insertWalletSessionSchema = createInsertSchema(walletSessions).omit({
  id: true,
  createdAt: true,
  lastActivity: true,
});

export const insertTransactionSchema = createInsertSchema(transactions).omit({
  id: true,
  createdAt: true,
  processedAt: true,
});

export const insertWalletConnectionSchema = createInsertSchema(walletConnections).omit({
  id: true,
  connectedAt: true,
  lastUsed: true,
});

export const insertWalletSpendingTrackingSchema = createInsertSchema(walletSpendingTracking).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// Type exports
export type InsertWallet = z.infer<typeof insertWalletSchema>;
export type Wallet = typeof wallets.$inferSelect;

export type InsertWalletSession = z.infer<typeof insertWalletSessionSchema>;
export type WalletSession = typeof walletSessions.$inferSelect;

export type InsertTransaction = z.infer<typeof insertTransactionSchema>;
export type Transaction = typeof transactions.$inferSelect;

export type InsertWalletConnection = z.infer<typeof insertWalletConnectionSchema>;
export type WalletConnection = typeof walletConnections.$inferSelect;

export type InsertWalletSpendingTracking = z.infer<typeof insertWalletSpendingTrackingSchema>;
export type WalletSpendingTracking = typeof walletSpendingTracking.$inferSelect;
