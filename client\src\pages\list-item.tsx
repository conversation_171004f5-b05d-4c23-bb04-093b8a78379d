import React, { useState } from 'react';
import { useLocation, useRoute } from 'wouter';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Loader2,
  Image as ImageIcon,
  Info,
  AlertTriangle,
  Package,
  PackageX,
  PackageCheck,
  CheckCircle2,
  ShieldCheck,
  UserCheck,
  ShoppingBag
} from 'lucide-react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';
import { useCategoryOptions } from '@/hooks/use-categories';
import { apiRequest } from '@/lib/queryClient';
import { isBase64Image } from '@/lib/utils';
import ImageUpload from '@/components/image-upload';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

// Schema for product listing
const productSchema = z.object({
  title: z.string().min(5, { message: 'Title must be at least 5 characters' }).max(100),
  description: z.string().min(20, { message: 'Description must be at least 20 characters' }).max(1000),
  price: z.string().refine(
    (val) => !isNaN(parseFloat(val)) && parseFloat(val) > 0,
    { message: 'Price must be a positive number' }
  ),
  imageUrl: z.string().refine(
    (val) => val === '' || val.startsWith('http') || isBase64Image(val),
    { message: 'Please upload an image or enter a valid image URL' }
  ),
  tags: z.string().min(3, { message: 'Please add at least one tag' }),
  category: z.string().min(1, { message: 'Please select a category' }),
  listingType: z.enum(['single', 'multiple', 'bulk']),
  quantity: z.string().optional(),
  condition: z.string().min(1, { message: 'Please select a condition' }),
  color: z.string().optional(),
});

type ProductFormValues = z.infer<typeof productSchema>;

const ListItemPage: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, params] = useRoute<any>('/list-item');
  const [, setLocation] = useLocation();
  const [showTagsHelp, setShowTagsHelp] = useState(false);

  // Get categories from database
  const { options: categoryOptions, isLoading: categoriesLoading } = useCategoryOptions();

  // Check seller verification status
  const { data: sellerData } = useQuery({
    queryKey: ['/api/sellers/verification'],
    enabled: !!user,
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // All users can list items - they become sellers automatically when they list their first item
  const hasIdentityVerification = user?.identityVerified || sellerData?.verification_status === 'approved';

  // Parse URL query parameters to get prefilled values
  const queryParams = new URLSearchParams(window.location.search);

  // Set up form with validation
  const form = useForm<ProductFormValues>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      title: queryParams.get('title') || '',
      description: queryParams.get('description') || '',
      price: queryParams.get('price') || '',
      imageUrl: queryParams.get('imageUrl') || '',
      tags: queryParams.get('tags') || '',
      category: queryParams.get('category') || '',
      listingType: 'single',
      quantity: '1',
      condition: queryParams.get('condition') || 'used',
      color: queryParams.get('color') || '',
    },
  });

  // Watch listing type
  const watchListingType = form.watch('listingType');

  // Submit mutation
  const listItemMutation = useMutation({
    mutationFn: async (data: ProductFormValues) => {
      // Determine if this is a bulk buy, multi-quantity or single item listing
      const isBulkBuy = data.listingType === 'bulk';
      const isMultipleItems = data.listingType === 'multiple';
      const quantity = isMultipleItems ? parseInt(data.quantity || '1') : 1;

      return apiRequest('/api/products', {
        method: 'POST',
        body: JSON.stringify({
          ...data,
          price: parseFloat(data.price),
          tags: data.tags.split(',').map(tag => tag.trim()),
          isBulkBuy,
          quantity: isBulkBuy ? 100 : quantity,
        }),
      });
    },
    onSuccess: () => {
      toast({
        title: 'Item Listed Successfully',
        description: 'Your item has been listed and is now available for buyers.',
      });
      form.reset();
      queryClient.invalidateQueries({ queryKey: ['/api/products'] });
      setLocation('/my-listings');
    },
    onError: (error: any) => {
      toast({
        title: 'Error Listing Item',
        description: error?.message || 'There was an error listing your item. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Handle form submission
  const onSubmit = (data: ProductFormValues) => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'You must be logged in to list an item.',
        variant: 'destructive',
        action: (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setLocation('/auth?redirect=/list-item')}
          >
            Sign In
          </Button>
        ),
      });
      return;
    }

    // Show info about identity verification if not completed
    if (!hasIdentityVerification) {
      toast({
        title: 'Item Listed Successfully',
        description: 'Your item is now live! Complete identity verification to access SafeSphere and earn more trust points.',
        variant: 'default',
      });
    }

    listItemMutation.mutate(data);
  };



  const conditions = [
    { value: 'new', label: 'New' },
    { value: 'like_new', label: 'Like New' },
    { value: 'excellent', label: 'Excellent' },
    { value: 'good', label: 'Good' },
    { value: 'fair', label: 'Fair' },
    { value: 'poor', label: 'Poor' },
  ];

  return (
    <div className="container mx-auto py-8 px-4">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="text-2xl">List an Item for Sale</CardTitle>
          <CardDescription>
            Fill out the form below to list your item on Daswos marketplace.
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {!user ? (
            <Alert className="mb-4 border-blue-500 bg-blue-50">
              <Info className="h-4 w-4 text-blue-500" />
              <AlertTitle className="text-blue-800">Sign In to List Items</AlertTitle>
              <AlertDescription className="text-blue-700">
                You need to create an account or sign in to list items. Creating an account gives you 30 trust points and you'll become a seller automatically when you list your first item.
              </AlertDescription>
            </Alert>
          ) : !hasIdentityVerification ? (
            <Alert className="mb-4 border-blue-500 bg-blue-50">
              <Info className="h-4 w-4 text-blue-500" />
              <AlertTitle className="text-blue-800">Ready to List Your Item</AlertTitle>
              <AlertDescription className="text-blue-700 space-y-2">
                <p>You can list items right away! Complete identity verification to access SafeSphere and earn additional trust points.</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  onClick={() => setLocation('/seller-verification')}
                >
                  <ShieldCheck className="h-4 w-4 mr-2" />
                  Complete Verification (Optional)
                </Button>
              </AlertDescription>
            </Alert>
          ) : (
            <Alert className="mb-4 border-green-500 bg-green-50">
              <CheckCircle2 className="h-4 w-4 text-green-500" />
              <AlertTitle className="text-green-800">Verified SafeSphere Seller</AlertTitle>
              <AlertDescription className="text-green-700">
                Your verified status gives you 70 trust points and increases buyer confidence.
              </AlertDescription>
            </Alert>
          )}

          {/* Progress Steps */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">Your Seller Progress</h3>
              <span className="text-xs text-muted-foreground">
                {user ? 'Complete steps to increase trust' : 'Sign in to start'}
              </span>
            </div>
            <div className="space-y-3">
              {[
                {
                  id: 'account',
                  icon: UserCheck,
                  label: 'Create Account',
                  completed: !!user,
                  points: 30
                },
                {
                  id: 'list',
                  icon: ShoppingBag,
                  label: 'List First Item',
                  completed: user?.isSeller, // User becomes seller when they list first item
                  points: 0 // No additional points, just becomes seller
                },
                {
                  id: 'verify',
                  icon: ShieldCheck,
                  label: 'Verify Identity (Optional)',
                  completed: hasIdentityVerification,
                  points: 40
                }
              ].map((step) => {
                const Icon = step.icon;
                return (
                  <div key={step.id} className="flex items-center gap-3">
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                      step.completed
                        ? 'bg-green-100 text-green-600'
                        : 'bg-gray-100 text-gray-400'
                    }`}>
                      <Icon className="h-4 w-4" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className={`text-sm ${step.completed ? 'font-medium' : 'text-muted-foreground'}`}>
                          {step.label}
                        </span>
                        {step.points > 0 && (
                          <span className="text-xs px-2 py-0.5 rounded-full bg-primary/10 text-primary">
                            +{step.points} points
                          </span>
                        )}
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                        <div
                          className={`h-1.5 rounded-full ${
                            step.completed ? 'bg-green-500' : 'bg-gray-200'
                          }`}
                          style={{ width: step.completed ? '100%' : '0%' }}
                        />
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </CardContent>

        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Item Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter a clear, descriptive title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price ($)</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.01" min="0" placeholder="0.00" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {categoriesLoading ? (
                            <SelectItem value="" disabled>Loading categories...</SelectItem>
                          ) : categoryOptions.length > 0 ? (
                            categoryOptions.map((category) => (
                              <SelectItem key={category.value} value={category.value}>
                                {category.label}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="" disabled>No categories available</SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Provide detailed information about your item, including condition, specifications, and any other relevant details."
                        rows={6}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center justify-between">
                      <FormLabel>Tags</FormLabel>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowTagsHelp(!showTagsHelp)}
                        className="h-6 px-2 text-xs"
                      >
                        <Info className="h-3 w-3 mr-1" />
                        Help
                      </Button>
                    </div>
                    {showTagsHelp && (
                      <p className="text-sm text-muted-foreground mb-2">
                        Enter comma-separated tags to help buyers find your item (e.g., "wireless, headphones, bluetooth")
                      </p>
                    )}
                    <FormControl>
                      <Input placeholder="Enter comma-separated tags" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="imageUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Product Image</FormLabel>
                    <FormControl>
                      <ImageUpload
                        value={field.value}
                        onChange={field.onChange}
                        maxSizeMB={5}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="condition"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Condition</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select condition" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {conditions.map((condition) => (
                          <SelectItem key={condition.value} value={condition.value}>
                            {condition.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="color"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Color (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter the color of your item" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="listingType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Listing Type</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="grid grid-cols-1 md:grid-cols-3 gap-4"
                      >
                        <div className="flex items-start space-x-2 border p-4 rounded-lg hover:bg-slate-50 cursor-pointer">
                          <RadioGroupItem value="single" id="single" className="mt-1" />
                          <div className="grid gap-1.5">
                            <Label htmlFor="single" className="font-medium flex items-center">
                              <Package className="h-4 w-4 mr-2 text-blue-500" />
                              Single Item
                            </Label>
                            <p className="text-xs text-muted-foreground">
                              List one individual item for sale
                            </p>
                          </div>
                        </div>

                        <div className="flex items-start space-x-2 border p-4 rounded-lg hover:bg-slate-50 cursor-pointer">
                          <RadioGroupItem value="multiple" id="multiple" className="mt-1" />
                          <div className="grid gap-1.5">
                            <Label htmlFor="multiple" className="font-medium flex items-center">
                              <PackageCheck className="h-4 w-4 mr-2 text-green-500" />
                              Multiple Items
                            </Label>
                            <p className="text-xs text-muted-foreground">
                              List 2-100 identical items
                            </p>
                          </div>
                        </div>

                        <div className="flex items-start space-x-2 border p-4 rounded-lg hover:bg-slate-50 cursor-pointer">
                          <RadioGroupItem value="bulk" id="bulk" className="mt-1" />
                          <div className="grid gap-1.5">
                            <Label htmlFor="bulk" className="font-medium flex items-center">
                              <PackageX className="h-4 w-4 mr-2 text-orange-500" />
                              Bulk Buy
                            </Label>
                            <p className="text-xs text-muted-foreground">
                              Large quantity (100+ items)
                            </p>
                          </div>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {watchListingType === 'multiple' && (
                <FormField
                  control={form.control}
                  name="quantity"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Quantity Available</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="2"
                          max="100"
                          {...field}
                          onChange={(e) => {
                            const val = parseInt(e.target.value);
                            if (val < 2) {
                              field.onChange('2');
                            } else if (val > 100) {
                              field.onChange('100');
                            } else {
                              field.onChange(e.target.value);
                            }
                          }}
                        />
                      </FormControl>
                      <FormDescription>
                        Enter a quantity between 2-100 items
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <div className="flex justify-end space-x-4 pt-4">
                <Button type="button" variant="outline" onClick={() => form.reset()}>
                  Reset Form
                </Button>
                <Button
                  type="submit"
                  disabled={listItemMutation.isPending}
                  className="gap-2"
                >
                  {listItemMutation.isPending && <Loader2 className="h-4 w-4 animate-spin" />}
                  List Item for Sale
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default ListItemPage;