-- Migration: Add Robot Positioning Algorithm Tables
-- Created: 2024-01-XX
-- Description: Adds tables for database-driven robot positioning algorithms with hourly rotation

-- Create robot positioning algorithms table
CREATE TABLE IF NOT EXISTS "robot_positioning_algorithms" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"positions" jsonb NOT NULL,
	"screen_width" integer DEFAULT 1920 NOT NULL,
	"screen_height" integer DEFAULT 1080 NOT NULL,
	"total_positions" integer NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp
);

-- Create user algorithm assignments table
CREATE TABLE IF NOT EXISTS "user_algorithm_assignments" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"algorithm_id" integer NOT NULL,
	"current_position_index" integer DEFAULT 0 NOT NULL,
	"assigned_at" timestamp DEFAULT now() NOT NULL,
	"expires_at" timestamp NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL
);

-- Create algorithm rotation log table
CREATE TABLE IF NOT EXISTS "algorithm_rotation_log" (
	"id" serial PRIMARY KEY NOT NULL,
	"rotation_time" timestamp DEFAULT now() NOT NULL,
	"total_users" integer NOT NULL,
	"total_algorithms" integer NOT NULL,
	"rotation_details" jsonb NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);

-- Add foreign key constraints
DO $$ BEGIN
 ALTER TABLE "user_algorithm_assignments" ADD CONSTRAINT "user_algorithm_assignments_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "user_algorithm_assignments" ADD CONSTRAINT "user_algorithm_assignments_algorithm_id_robot_positioning_algorithms_id_fk" FOREIGN KEY ("algorithm_id") REFERENCES "robot_positioning_algorithms"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "idx_robot_algorithms_active" ON "robot_positioning_algorithms" ("is_active");
CREATE INDEX IF NOT EXISTS "idx_user_assignments_user_active" ON "user_algorithm_assignments" ("user_id", "is_active");
CREATE INDEX IF NOT EXISTS "idx_user_assignments_expires" ON "user_algorithm_assignments" ("expires_at");
CREATE INDEX IF NOT EXISTS "idx_rotation_log_time" ON "algorithm_rotation_log" ("rotation_time");

-- Add comments for documentation
COMMENT ON TABLE "robot_positioning_algorithms" IS 'Stores positioning algorithms with 1200+ unique positions for robot placement';
COMMENT ON TABLE "user_algorithm_assignments" IS 'Tracks which algorithm each user is currently assigned to, rotated hourly';
COMMENT ON TABLE "algorithm_rotation_log" IS 'Logs algorithm rotation events for monitoring and debugging';

COMMENT ON COLUMN "robot_positioning_algorithms"."positions" IS 'JSONB array of {x, y} position objects';
COMMENT ON COLUMN "robot_positioning_algorithms"."total_positions" IS 'Number of positions in the algorithm (should be 1200+)';
COMMENT ON COLUMN "user_algorithm_assignments"."current_position_index" IS 'Current position index in the algorithm sequence';
COMMENT ON COLUMN "user_algorithm_assignments"."expires_at" IS 'When this assignment expires (1 hour from assignment)';
COMMENT ON COLUMN "algorithm_rotation_log"."rotation_details" IS 'JSON object with user->algorithm mapping details';
