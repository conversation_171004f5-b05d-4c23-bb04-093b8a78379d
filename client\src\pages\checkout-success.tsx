import React, { useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, Package, Home } from 'lucide-react';
import { apiRequest } from '@/lib/queryClient';

const CheckoutSuccessPage: React.FC = () => {
  const [, setLocation] = useLocation();
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [orderDetails, setOrderDetails] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get session ID from URL params
    const urlParams = new URLSearchParams(window.location.search);
    const id = urlParams.get('session_id');
    
    if (id) {
      setSessionId(id);
      fetchOrderDetails(id);
    } else {
      setLoading(false);
    }
  }, []);

  const fetchOrderDetails = async (sessionId: string) => {
    try {
      // You could fetch order details from your backend using the session ID
      // For now, we'll just show a success message
      setLoading(false);
    } catch (error) {
      console.error('Error fetching order details:', error);
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading order confirmation...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <Card>
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <CardTitle className="text-2xl text-green-800">Payment Successful!</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-6">
          <div>
            <p className="text-gray-600 mb-2">
              Thank you for your purchase! Your order has been confirmed and is being processed.
            </p>
            {sessionId && (
              <p className="text-sm text-gray-500">
                Session ID: {sessionId}
              </p>
            )}
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <Package className="w-5 h-5 text-blue-600 mr-2" />
              <h3 className="font-medium text-blue-800">What's Next?</h3>
            </div>
            <p className="text-sm text-blue-700">
              You'll receive an email confirmation shortly with your order details and tracking information.
            </p>
          </div>

          <div className="space-y-3">
            <Button 
              className="w-full" 
              onClick={() => setLocation('/')}
            >
              <Home className="w-4 h-4 mr-2" />
              Continue Shopping
            </Button>
            
            <Button 
              variant="outline" 
              className="w-full"
              onClick={() => setLocation('/track-order')}
            >
              <Package className="w-4 h-4 mr-2" />
              Track Your Order
            </Button>
          </div>

          <div className="text-xs text-gray-500 pt-4 border-t">
            <p>
              If you have any questions about your order, please contact our support team.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CheckoutSuccessPage;
